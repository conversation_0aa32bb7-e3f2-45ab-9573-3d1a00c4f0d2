import { createClient } from '@supabase/supabase-js';
import { supabaseConfig } from './index';
import { logger } from '../utils/logger';

// Create Supabase client
let supabase: any = null;

try {
  if (supabaseConfig.url && supabaseConfig.serviceRoleKey) {
    supabase = createClient(
      supabaseConfig.url,
      supabaseConfig.serviceRoleKey,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );
    
    logger.info('✅ Supabase client initialized successfully', {
      url: supabaseConfig.url,
      hasServiceKey: !!supabaseConfig.serviceRoleKey
    });
  } else {
    logger.warn('⚠️ Supabase configuration missing, creating mock client');
    
    // Create a mock client for development
    supabase = {
      from: (table: string) => ({
        select: () => ({ 
          eq: () => ({ 
            single: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
            limit: () => Promise.resolve({ data: [], error: null })
          }),
          order: () => ({ 
            limit: () => Promise.resolve({ data: [], error: null })
          })
        }),
        insert: () => ({ 
          select: () => ({ 
            single: () => Promise.resolve({ data: { id: 'mock-id' }, error: null })
          })
        }),
        update: () => ({ 
          eq: () => Promise.resolve({ error: null })
        })
      })
    };
  }
} catch (error) {
  logger.error('❌ Failed to initialize Supabase client', { error });
  
  // Create a mock client as fallback
  supabase = {
    from: (table: string) => ({
      select: () => ({ 
        eq: () => ({ 
          single: () => Promise.resolve({ data: null, error: new Error('Supabase initialization failed') }),
          limit: () => Promise.resolve({ data: [], error: null })
        }),
        order: () => ({ 
          limit: () => Promise.resolve({ data: [], error: null })
        })
      }),
      insert: () => ({ 
        select: () => ({ 
          single: () => Promise.resolve({ data: { id: 'mock-id' }, error: null })
        })
      }),
      update: () => ({ 
        eq: () => Promise.resolve({ error: null })
      })
    })
  };
}

export { supabase };
export default supabase;
