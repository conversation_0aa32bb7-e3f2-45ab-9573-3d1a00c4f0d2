{"name": "@freela/api", "version": "1.0.0", "description": "Freela Syria API Server", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node ./dist/index.js", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "dependencies": {"@freela/database": "file:../../packages/database", "@freela/i18n": "file:../../packages/i18n", "@freela/types": "file:../../packages/types", "@freela/utils": "file:../../packages/utils", "@supabase/supabase-js": "^2.50.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "nanoid": "^5.0.4", "nodemailer": "^6.9.7", "redis": "^4.6.10", "socket.io": "^4.7.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "winston": "^3.11.0", "ws": "^8.14.2", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/supertest": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/ws": "^8.5.10", "jest": "^29.7.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsx": "^4.19.4", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0"}}