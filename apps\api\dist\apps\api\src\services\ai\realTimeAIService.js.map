{"version": 3, "file": "realTimeAIService.js", "sourceRoot": "", "sources": ["../../../../../../src/services/ai/realTimeAIService.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAGH,4DAAyD;AACzD,uDAAoD;AACpD,+CAA4C;AA0B5C,MAAa,iBAAiB;IACpB,EAAE,CAAiB;IACnB,iBAAiB,GAA6B,IAAI,GAAG,EAAE,CAAC,CAAC,gCAAgC;IACzF,YAAY,GAA6B,IAAI,GAAG,EAAE,CAAC,CAAC,8BAA8B;IAClF,gBAAgB,GAAoC,IAAI,GAAG,EAAE,CAAC,CAAC,sBAAsB;IAE7F,YAAY,EAAkB;QAC5B,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,EAAE;YAClC,eAAM,CAAC,IAAI,CAAC,0CAA0C,EAAE,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YAEjF,4BAA4B;YAC5B,MAAM,CAAC,EAAE,CAAC,iBAAiB,EAAE,KAAK,EAAE,IAA0D,EAAE,EAAE;gBAChG,IAAI,CAAC;oBACH,mDAAmD;oBACnD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;oBACxF,IAAI,CAAC,OAAO,EAAE,CAAC;wBACb,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC,CAAC;wBACvE,OAAO;oBACT,CAAC;oBAED,oBAAoB;oBACpB,MAAM,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;oBAE5C,mBAAmB;oBACnB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;wBAChD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;oBACxD,CAAC;oBACD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAE,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBAE3D,sBAAsB;oBACtB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;wBACxC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;oBAChD,CAAC;oBACD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAE,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAExD,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;wBAC/B,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,MAAM,EAAE,WAAW;wBACnB,QAAQ,EAAE;4BACR,kBAAkB,EAAE,IAAI;4BACxB,gBAAgB,EAAE,IAAI;4BACtB,gBAAgB,EAAE,IAAI;4BACtB,eAAe,EAAE,IAAI;yBACtB;qBACF,CAAC,CAAC;oBAEH,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE;wBACtC,QAAQ,EAAE,MAAM,CAAC,EAAE;wBACnB,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,MAAM,EAAE,IAAI,CAAC,MAAM;qBACpB,CAAC,CAAC;gBAEL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;oBAC1E,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC,CAAC;gBACjE,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,mCAAmC;YACnC,MAAM,CAAC,EAAE,CAAC,iBAAiB,EAAE,KAAK,EAAE,IAMnC,EAAE,EAAE;gBACH,IAAI,CAAC;oBACH,yBAAyB;oBACzB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,SAAS,EAAE;wBAC1C,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,MAAM,EAAE,YAAY;wBACpB,QAAQ,EAAE,EAAE;wBACZ,WAAW,EAAE,kBAAkB;qBAChC,CAAC,CAAC;oBAEH,0CAA0C;oBAC1C,MAAM,MAAM,GAAG,MAAM,iCAAe,CAAC,cAAc,CACjD,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,WAAW,IAAI,MAAM,EAC1B,IAAI,CAAC,QAAQ,IAAI,EAAE,CACpB,CAAC;oBAEF,2BAA2B;oBAC3B,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,SAAS,EAAE;wBAC1C,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,MAAM,EAAE,UAAU;wBAClB,QAAQ,EAAE,GAAG;wBACb,WAAW,EAAE,aAAa;qBAC3B,CAAC,CAAC;oBAEH,iDAAiD;oBACjD,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE;wBAC7D,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,WAAW,EAAE;4BACX,OAAO,EAAE,IAAI,CAAC,OAAO;4BACrB,IAAI,EAAE,IAAI,CAAC,WAAW,IAAI,MAAM;4BAChC,SAAS,EAAE,IAAI,IAAI,EAAE;yBACtB;wBACD,UAAU,EAAE;4BACV,OAAO,EAAE,MAAM,CAAC,UAAU;4BAC1B,SAAS,EAAE,IAAI,IAAI,EAAE;4BACrB,aAAa,EAAE,MAAM,CAAC,aAAa;yBACpC;wBACD,eAAe,EAAE;4BACf,WAAW,EAAE,MAAM,CAAC,QAAQ;4BAC5B,cAAc,EAAE,MAAM,CAAC,cAAc;4BACrC,WAAW,EAAE,MAAM,CAAC,cAAc,IAAI,GAAG;yBAC1C;qBACF,CAAC,CAAC;oBAEH,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;wBAC5C,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,MAAM,EAAE,IAAI,CAAC,MAAM;wBACnB,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;wBAClC,QAAQ,EAAE,MAAM,CAAC,QAAQ;qBAC1B,CAAC,CAAC;gBAEL,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;oBAC7F,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE;wBACtB,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,OAAO,EAAE,gDAAgD;wBACzD,KAAK,EAAE,KAAK,EAAE,OAAO,IAAI,eAAe;qBACzC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,2BAA2B;YAC3B,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,IAA2C,EAAE,EAAE;gBACxE,MAAM,CAAC,EAAE,CAAC,cAAc,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE;oBAC5D,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,QAAQ,EAAE,IAAI;oBACd,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAA2C,EAAE,EAAE;gBACvE,MAAM,CAAC,EAAE,CAAC,cAAc,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE;oBAC5D,SAAS,EAAE,IAAI,CAAC,SAAS;oBACzB,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,QAAQ,EAAE,KAAK;oBACf,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,kCAAkC;YAClC,MAAM,CAAC,EAAE,CAAC,uBAAuB,EAAE,KAAK,EAAE,IAMzC,EAAE,EAAE;gBACH,IAAI,CAAC;oBACH,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,SAAS,EAAE;wBAC1C,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,MAAM,EAAE,WAAW;wBACnB,QAAQ,EAAE,EAAE;wBACZ,WAAW,EAAE,0BAA0B;qBACxC,CAAC,CAAC;oBAEH,kDAAkD;oBAClD,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,iCAAiC,CAAC;oBAE9E,2BAA2B;oBAC3B,MAAM,MAAM,GAAG,MAAM,iCAAe,CAAC,cAAc,CACjD,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,MAAM,EACX,aAAa,EACb,kBAAkB,EAClB;wBACE,SAAS,EAAE;4BACT,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,SAAS;4BAClC,aAAa,EAAE,IAAI;4BACnB,mBAAmB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW;yBACnE;qBACF,CACF,CAAC;oBAEF,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,SAAS,EAAE;wBAC1C,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,MAAM,EAAE,UAAU;wBAClB,QAAQ,EAAE,GAAG;qBACd,CAAC,CAAC;oBAEH,+BAA+B;oBAC/B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,iBAAiB,EAAE;wBACjE,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,aAAa;wBACb,UAAU,EAAE,MAAM,CAAC,UAAU;wBAC7B,aAAa,EAAE,MAAM,CAAC,aAAa;wBACnC,eAAe,EAAE;4BACf,WAAW,EAAE,MAAM,CAAC,QAAQ;4BAC5B,cAAc,EAAE,MAAM,CAAC,cAAc;yBACtC;qBACF,CAAC,CAAC;gBAEL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;oBACtF,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE;wBACzB,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,OAAO,EAAE,+BAA+B;qBACzC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,wBAAwB;YACxB,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,KAAK,EAAE,IAMjC,EAAE,EAAE;gBACH,IAAI,CAAC;oBACH,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,SAAS,EAAE;wBAC1C,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,MAAM,EAAE,WAAW;wBACnB,QAAQ,EAAE,EAAE;wBACZ,WAAW,EAAE,iBAAiB;qBAC/B,CAAC,CAAC;oBAEH,uEAAuE;oBACvE,MAAM,cAAc,GAAG,oBAAoB,IAAI,CAAC,YAAY,IAAI,WAAW,MAAM,IAAI,CAAC,WAAW,IAAI,EAAE;;;;;6CAKpE,CAAC;oBAEpC,2BAA2B;oBAC3B,MAAM,MAAM,GAAG,MAAM,iCAAe,CAAC,cAAc,CACjD,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,MAAM,EACX,cAAc,EACd,mBAAmB,EACnB;wBACE,SAAS,EAAE;4BACT,YAAY,EAAE,IAAI,CAAC,YAAY,IAAI,WAAW;4BAC9C,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;4BACnC,QAAQ,EAAE,IAAI;yBACf;qBACF,CACF,CAAC;oBAEF,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,SAAS,EAAE;wBAC1C,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,MAAM,EAAE,UAAU;wBAClB,QAAQ,EAAE,GAAG;qBACd,CAAC,CAAC;oBAEH,6BAA6B;oBAC7B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE;wBAChE,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,QAAQ,EAAE,cAAc;wBACxB,UAAU,EAAE,MAAM,CAAC,UAAU;wBAC7B,aAAa,EAAE,MAAM,CAAC,aAAa;wBACnC,eAAe,EAAE;4BACf,WAAW,EAAE,MAAM,CAAC,QAAQ;4BAC5B,cAAc,EAAE,MAAM,CAAC,cAAc;yBACtC;qBACF,CAAC,CAAC;gBAEL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;oBAC9E,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE;wBACzB,SAAS,EAAE,IAAI,CAAC,SAAS;wBACzB,OAAO,EAAE,qBAAqB;qBAC/B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,uBAAuB;YACvB,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE;gBAC3B,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACpC,eAAM,CAAC,IAAI,CAAC,+CAA+C,EAAE,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YACxF,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,gCAAgC;QAChC,mBAAQ;aACL,OAAO,CAAC,kBAAkB,CAAC;aAC3B,EAAE,CAAC,kBAAkB,EACpB,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAChE,CAAC,OAAO,EAAE,EAAE;YACV,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC;YAC5B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBACpE,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,SAAS,EAAE,OAAO,CAAC,UAAU;gBAC7B,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,WAAW,EAAE,OAAO,CAAC,YAAY;gBACjC,SAAS,EAAE,OAAO,CAAC,UAAU;gBAC7B,aAAa,EAAE,OAAO,CAAC,cAAc;aACtC,CAAC,CAAC;QACL,CAAC,CACF;aACA,SAAS,EAAE,CAAC;QAEf,+BAA+B;QAC/B,mBAAQ;aACL,OAAO,CAAC,kBAAkB,CAAC;aAC3B,EAAE,CAAC,kBAAkB,EACpB,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAChE,CAAC,OAAO,EAAE,EAAE;YACV,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC;YAC5B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBAC7D,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,WAAW,EAAE,OAAO,CAAC,YAAY;gBACjC,cAAc,EAAE,OAAO,CAAC,eAAe;gBACvC,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,aAAa,EAAE,OAAO,CAAC,cAAc;aACtC,CAAC,CAAC;QACL,CAAC,CACF;aACA,SAAS,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,SAAiB,EAAE,MAA0B;QAC1E,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,SAAiB,EAAE,MAAc,EAAE,KAAa;QAChF,IAAI,CAAC;YACH,kCAAkC;YAClC,4DAA4D;YAC5D,MAAM,OAAO,GAAG,MAAM,iCAAe,CAAC,UAAU,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YACpE,OAAO,OAAO,KAAK,IAAI,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC;YAC9E,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,QAAgB;QAC1C,iCAAiC;QACjC,KAAK,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC;YACtE,IAAI,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC5B,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC3B,IAAI,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;oBACzB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAC3C,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACI,yBAAyB,CAAC,SAAiB;QAChD,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,SAAiB,EAAE,KAAa,EAAE,IAAS;QACnE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC1D,CAAC;CACF;AAlYD,8CAkYC"}