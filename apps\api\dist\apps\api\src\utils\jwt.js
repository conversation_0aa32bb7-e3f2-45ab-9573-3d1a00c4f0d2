"use strict";
/**
 * JWT Utility for Freela Syria API
 * Provides comprehensive JWT token management with blacklisting support
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.jwtUtils = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const redis_1 = require("redis");
const errors_1 = require("./errors");
// JWT Configuration from environment
const JWT_CONFIG = {
    ACCESS_SECRET: process.env.JWT_ACCESS_SECRET || 'freela-syria-access-secret-2024',
    REFRESH_SECRET: process.env.JWT_REFRESH_SECRET || 'freela-syria-refresh-secret-2024',
    ACCESS_EXPIRES_IN: (process.env.JWT_ACCESS_EXPIRES_IN || '15m'),
    REFRESH_EXPIRES_IN: (process.env.JWT_REFRESH_EXPIRES_IN || '7d'),
    ISSUER: process.env.JWT_ISSUER || 'freela-syria-api',
    AUDIENCE: process.env.JWT_AUDIENCE || 'freela-syria-app',
};
// Redis client for token blacklisting (optional)
let redisClient = null;
// Initialize Redis client if available
const initializeRedis = async () => {
    try {
        if (process.env.REDIS_URL) {
            redisClient = (0, redis_1.createClient)({
                url: process.env.REDIS_URL,
            });
            await redisClient.connect();
            console.log('Redis connected for JWT blacklisting');
        }
    }
    catch (error) {
        console.warn('Redis not available for JWT blacklisting:', error.message);
        redisClient = null;
    }
};
// Initialize Redis on module load
initializeRedis();
/**
 * JWT Utilities Object
 */
exports.jwtUtils = {
    /**
     * Generate access token
     */
    generateAccessToken: (payload) => {
        try {
            return jsonwebtoken_1.default.sign(payload, JWT_CONFIG.ACCESS_SECRET, {
                expiresIn: JWT_CONFIG.ACCESS_EXPIRES_IN,
                issuer: JWT_CONFIG.ISSUER,
                audience: JWT_CONFIG.AUDIENCE,
            });
        }
        catch (error) {
            throw errors_1.createError.internalError({ operation: 'generateAccessToken', error: error.message }, 'فشل في إنشاء رمز الوصول');
        }
    },
    /**
     * Generate refresh token
     */
    generateRefreshToken: (payload) => {
        try {
            return jsonwebtoken_1.default.sign(payload, JWT_CONFIG.REFRESH_SECRET, {
                expiresIn: JWT_CONFIG.REFRESH_EXPIRES_IN,
                issuer: JWT_CONFIG.ISSUER,
                audience: JWT_CONFIG.AUDIENCE,
            });
        }
        catch (error) {
            throw errors_1.createError.internalError({ operation: 'generateRefreshToken', error: error.message }, 'فشل في إنشاء رمز التحديث');
        }
    },
    /**
     * Generate token pair (access + refresh)
     */
    generateTokenPair: (payload) => {
        const accessToken = exports.jwtUtils.generateAccessToken(payload);
        const refreshToken = exports.jwtUtils.generateRefreshToken(payload);
        // Calculate expiration times in seconds
        const accessExpiresIn = exports.jwtUtils.getTokenExpirationTime(JWT_CONFIG.ACCESS_EXPIRES_IN);
        const refreshExpiresIn = exports.jwtUtils.getTokenExpirationTime(JWT_CONFIG.REFRESH_EXPIRES_IN);
        return {
            accessToken,
            refreshToken,
            expiresIn: accessExpiresIn,
            refreshExpiresIn: refreshExpiresIn,
        };
    },
    /**
     * Verify access token
     */
    verifyAccessToken: async (token) => {
        try {
            // Check if token is blacklisted
            if (await exports.jwtUtils.isTokenBlacklisted(token)) {
                throw errors_1.createError.tokenInvalid('الرمز المميز محظور');
            }
            const decoded = jsonwebtoken_1.default.verify(token, JWT_CONFIG.ACCESS_SECRET, {
                issuer: JWT_CONFIG.ISSUER,
                audience: JWT_CONFIG.AUDIENCE,
            });
            return decoded;
        }
        catch (error) {
            if (error instanceof errors_1.AppError) {
                throw error;
            }
            const err = error;
            if (err.name === 'TokenExpiredError') {
                throw errors_1.createError.tokenExpired();
            }
            if (err.name === 'JsonWebTokenError') {
                throw errors_1.createError.tokenInvalid();
            }
            throw errors_1.createError.internalError({ operation: 'verifyAccessToken', error: error.message }, 'فشل في التحقق من رمز الوصول');
        }
    },
    /**
     * Verify refresh token
     */
    verifyRefreshToken: async (token) => {
        try {
            // Check if token is blacklisted
            if (await exports.jwtUtils.isTokenBlacklisted(token)) {
                throw errors_1.createError.tokenInvalid('رمز التحديث محظور');
            }
            const decoded = jsonwebtoken_1.default.verify(token, JWT_CONFIG.REFRESH_SECRET, {
                issuer: JWT_CONFIG.ISSUER,
                audience: JWT_CONFIG.AUDIENCE,
            });
            return decoded;
        }
        catch (error) {
            if (error instanceof errors_1.AppError) {
                throw error;
            }
            const err = error;
            if (err.name === 'TokenExpiredError') {
                throw errors_1.createError.tokenExpired();
            }
            if (err.name === 'JsonWebTokenError') {
                throw errors_1.createError.tokenInvalid();
            }
            throw errors_1.createError.internalError({ operation: 'verifyRefreshToken', error: error.message }, 'فشل في التحقق من رمز التحديث');
        }
    },
    /**
     * Refresh access token using refresh token
     */
    refreshAccessToken: async (refreshToken) => {
        const payload = await exports.jwtUtils.verifyRefreshToken(refreshToken);
        // Generate new token pair
        const newTokenPair = exports.jwtUtils.generateTokenPair({
            userId: payload.userId,
            email: payload.email,
            role: payload.role,
            sessionId: payload.sessionId,
        });
        // Blacklist the old refresh token
        await exports.jwtUtils.blacklistToken(refreshToken);
        return newTokenPair;
    },
    /**
     * Blacklist a token (logout)
     */
    blacklistToken: async (token) => {
        if (!redisClient) {
            console.warn('Redis not available for token blacklisting');
            return;
        }
        try {
            // Decode token to get expiration time
            const decoded = jsonwebtoken_1.default.decode(token);
            if (decoded && decoded.exp) {
                const ttl = decoded.exp - Math.floor(Date.now() / 1000);
                if (ttl > 0) {
                    await redisClient.setEx(`blacklist:${token}`, ttl, 'true');
                }
            }
        }
        catch (error) {
            console.error('Error blacklisting token:', error.message);
        }
    },
    /**
     * Check if token is blacklisted
     */
    isTokenBlacklisted: async (token) => {
        if (!redisClient) {
            return false; // If Redis is not available, assume token is not blacklisted
        }
        try {
            const result = await redisClient.get(`blacklist:${token}`);
            return result === 'true';
        }
        catch (error) {
            console.error('Error checking token blacklist:', error.message);
            return false; // Fail open - assume token is not blacklisted
        }
    },
    /**
     * Get token expiration time in seconds
     */
    getTokenExpirationTime: (expiresIn) => {
        if (typeof expiresIn === 'number') {
            return expiresIn;
        }
        // Parse string format like '15m', '7d', '1h'
        const match = expiresIn.match(/^(\d+)([smhd])$/);
        if (!match) {
            throw new Error(`Invalid expiresIn format: ${expiresIn}`);
        }
        const value = parseInt(match[1]);
        const unit = match[2];
        switch (unit) {
            case 's': return value;
            case 'm': return value * 60;
            case 'h': return value * 60 * 60;
            case 'd': return value * 60 * 60 * 24;
            default: throw new Error(`Invalid time unit: ${unit}`);
        }
    },
    /**
     * Extract token from Authorization header
     */
    extractTokenFromHeader: (authHeader) => {
        if (!authHeader) {
            return null;
        }
        const parts = authHeader.split(' ');
        if (parts.length !== 2 || parts[0] !== 'Bearer') {
            return null;
        }
        return parts[1];
    },
};
// Export default as jwtUtils for convenience
exports.default = exports.jwtUtils;
//# sourceMappingURL=jwt.js.map