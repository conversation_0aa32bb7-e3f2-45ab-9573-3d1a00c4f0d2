/**
 * Real-time AI Service for Phase 3
 * WebSocket-based real-time AI conversation management
 */

import { Server as SocketIOServer } from 'socket.io';
import { supabase } from '@freela/database/src/supabase';
import { phase3AIService } from './phase3AIService';
import { logger } from '../../utils/logger';
import { createError } from '../../utils/errors';

export interface RealTimeAIEvent {
  type: 'message' | 'typing' | 'processing' | 'analysis_complete' | 'session_update';
  sessionId: string;
  userId: string;
  data: any;
  timestamp: Date;
}

export interface TypingIndicator {
  sessionId: string;
  userId: string;
  isTyping: boolean;
  timestamp: Date;
}

export interface AIProcessingStatus {
  sessionId: string;
  status: 'processing' | 'analyzing' | 'extracting' | 'responding' | 'complete';
  progress: number;
  estimatedTime?: number;
  currentTask?: string;
}

export class RealTimeAIService {
  private io: SocketIOServer;
  private activeConnections: Map<string, Set<string>> = new Map(); // sessionId -> Set of socketIds
  private userSessions: Map<string, Set<string>> = new Map(); // userId -> Set of sessionIds
  private processingStatus: Map<string, AIProcessingStatus> = new Map(); // sessionId -> status

  constructor(io: SocketIOServer) {
    this.io = io;
    this.setupSocketHandlers();
    this.setupSupabaseRealTime();
  }

  /**
   * Setup Socket.IO event handlers
   */
  private setupSocketHandlers(): void {
    this.io.on('connection', (socket) => {
      logger.info('Client connected to real-time AI service', { socketId: socket.id });

      // Join AI conversation room
      socket.on('join_ai_session', async (data: { sessionId: string; userId: string; token: string }) => {
        try {
          // Verify user authentication and session ownership
          const isValid = await this.verifySessionAccess(data.sessionId, data.userId, data.token);
          if (!isValid) {
            socket.emit('error', { message: 'Unauthorized access to AI session' });
            return;
          }

          // Join session room
          socket.join(`ai_session_${data.sessionId}`);
          
          // Track connection
          if (!this.activeConnections.has(data.sessionId)) {
            this.activeConnections.set(data.sessionId, new Set());
          }
          this.activeConnections.get(data.sessionId)!.add(socket.id);

          // Track user sessions
          if (!this.userSessions.has(data.userId)) {
            this.userSessions.set(data.userId, new Set());
          }
          this.userSessions.get(data.userId)!.add(data.sessionId);

          socket.emit('joined_ai_session', { 
            sessionId: data.sessionId,
            status: 'connected',
            features: {
              realTimeProcessing: true,
              typingIndicators: true,
              progressTracking: true,
              instantAnalysis: true
            }
          });

          logger.info('Client joined AI session', { 
            socketId: socket.id, 
            sessionId: data.sessionId, 
            userId: data.userId 
          });

        } catch (error) {
          logger.error('Failed to join AI session', { error, socketId: socket.id });
          socket.emit('error', { message: 'Failed to join AI session' });
        }
      });

      // Handle real-time message sending
      socket.on('send_ai_message', async (data: {
        sessionId: string;
        userId: string;
        message: string;
        messageType?: string;
        metadata?: any;
      }) => {
        try {
          // Emit processing status
          this.updateProcessingStatus(data.sessionId, {
            sessionId: data.sessionId,
            status: 'processing',
            progress: 10,
            currentTask: 'تحليل الرسالة...'
          });

          // Process message with Phase 3 AI service
          const result = await phase3AIService.processMessage(
            data.sessionId,
            data.userId,
            data.message,
            data.messageType || 'text',
            data.metadata || {}
          );

          // Update processing status
          this.updateProcessingStatus(data.sessionId, {
            sessionId: data.sessionId,
            status: 'complete',
            progress: 100,
            currentTask: 'تم الانتهاء'
          });

          // Emit AI response to all clients in the session
          this.io.to(`ai_session_${data.sessionId}`).emit('ai_response', {
            sessionId: data.sessionId,
            userMessage: {
              content: data.message,
              type: data.messageType || 'text',
              timestamp: new Date()
            },
            aiResponse: {
              content: result.aiResponse,
              timestamp: new Date(),
              extractedData: result.extractedData
            },
            sessionProgress: {
              currentStep: result.nextStep,
              completionRate: result.completionRate,
              isCompleted: result.completionRate >= 100
            }
          });

          logger.info('Real-time AI message processed', {
            sessionId: data.sessionId,
            userId: data.userId,
            messageLength: data.message.length,
            nextStep: result.nextStep
          });

        } catch (error) {
          logger.error('Failed to process real-time AI message', { error, sessionId: data.sessionId });
          socket.emit('ai_error', { 
            sessionId: data.sessionId,
            message: 'فشل في معالجة الرسالة. يرجى المحاولة مرة أخرى.',
            error: error.message 
          });
        }
      });

      // Handle typing indicators
      socket.on('typing_start', (data: { sessionId: string; userId: string }) => {
        socket.to(`ai_session_${data.sessionId}`).emit('user_typing', {
          sessionId: data.sessionId,
          userId: data.userId,
          isTyping: true,
          timestamp: new Date()
        });
      });

      socket.on('typing_stop', (data: { sessionId: string; userId: string }) => {
        socket.to(`ai_session_${data.sessionId}`).emit('user_typing', {
          sessionId: data.sessionId,
          userId: data.userId,
          isTyping: false,
          timestamp: new Date()
        });
      });

      // Handle voice message processing
      socket.on('process_voice_message', async (data: {
        sessionId: string;
        userId: string;
        audioData: string; // Base64 encoded audio
        transcription?: string;
        dialect?: string;
      }) => {
        try {
          this.updateProcessingStatus(data.sessionId, {
            sessionId: data.sessionId,
            status: 'analyzing',
            progress: 20,
            currentTask: 'تحليل الرسالة الصوتية...'
          });

          // For now, use provided transcription or simulate
          const transcription = data.transcription || 'تم تحويل الرسالة الصوتية إلى نص';

          // Process as voice message
          const result = await phase3AIService.processMessage(
            data.sessionId,
            data.userId,
            transcription,
            'voice_transcript',
            {
              voiceData: {
                dialect: data.dialect || 'general',
                originalAudio: true,
                transcriptionMethod: data.transcription ? 'provided' : 'simulated'
              }
            }
          );

          this.updateProcessingStatus(data.sessionId, {
            sessionId: data.sessionId,
            status: 'complete',
            progress: 100
          });

          // Emit voice processing result
          this.io.to(`ai_session_${data.sessionId}`).emit('voice_processed', {
            sessionId: data.sessionId,
            transcription,
            aiResponse: result.aiResponse,
            extractedData: result.extractedData,
            sessionProgress: {
              currentStep: result.nextStep,
              completionRate: result.completionRate
            }
          });

        } catch (error) {
          logger.error('Failed to process voice message', { error, sessionId: data.sessionId });
          socket.emit('voice_error', { 
            sessionId: data.sessionId,
            message: 'فشل في معالجة الرسالة الصوتية'
          });
        }
      });

      // Handle image analysis
      socket.on('analyze_image', async (data: {
        sessionId: string;
        userId: string;
        imageData: string; // Base64 encoded image
        description?: string;
        analysisType?: string;
      }) => {
        try {
          this.updateProcessingStatus(data.sessionId, {
            sessionId: data.sessionId,
            status: 'analyzing',
            progress: 30,
            currentTask: 'تحليل الصورة...'
          });

          // Simulate image analysis (in production, integrate with GPT-4 Vision)
          const analysisResult = `تم تحليل الصورة (${data.analysisType || 'portfolio'}). ${data.description || ''}
          
نتائج التحليل:
- نوع المحتوى: عمل إبداعي
- الجودة: عالية
- المهارات المستنتجة: تصميم، إبداع، احترافية`;

          // Process as image message
          const result = await phase3AIService.processMessage(
            data.sessionId,
            data.userId,
            analysisResult,
            'image_description',
            {
              imageData: {
                analysisType: data.analysisType || 'portfolio',
                description: data.description || '',
                hasImage: true
              }
            }
          );

          this.updateProcessingStatus(data.sessionId, {
            sessionId: data.sessionId,
            status: 'complete',
            progress: 100
          });

          // Emit image analysis result
          this.io.to(`ai_session_${data.sessionId}`).emit('image_analyzed', {
            sessionId: data.sessionId,
            analysis: analysisResult,
            aiResponse: result.aiResponse,
            extractedData: result.extractedData,
            sessionProgress: {
              currentStep: result.nextStep,
              completionRate: result.completionRate
            }
          });

        } catch (error) {
          logger.error('Failed to analyze image', { error, sessionId: data.sessionId });
          socket.emit('image_error', { 
            sessionId: data.sessionId,
            message: 'فشل في تحليل الصورة'
          });
        }
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        this.handleDisconnection(socket.id);
        logger.info('Client disconnected from real-time AI service', { socketId: socket.id });
      });
    });
  }

  /**
   * Setup Supabase real-time subscriptions
   */
  private setupSupabaseRealTime(): void {
    // Subscribe to AI chat messages
    supabase
      .channel('ai_chat_messages')
      .on('postgres_changes', 
        { event: 'INSERT', schema: 'public', table: 'ai_chat_messages' },
        (payload) => {
          const message = payload.new;
          this.io.to(`ai_session_${message.session_id}`).emit('new_ai_message', {
            id: message.id,
            sessionId: message.session_id,
            role: message.role,
            content: message.content,
            messageType: message.message_type,
            timestamp: message.created_at,
            extractedData: message.extracted_data
          });
        }
      )
      .subscribe();

    // Subscribe to session updates
    supabase
      .channel('ai_chat_sessions')
      .on('postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'ai_chat_sessions' },
        (payload) => {
          const session = payload.new;
          this.io.to(`ai_session_${session.id}`).emit('session_updated', {
            sessionId: session.id,
            currentStep: session.current_step,
            completionRate: session.completion_rate,
            status: session.status,
            extractedData: session.extracted_data
          });
        }
      )
      .subscribe();
  }

  /**
   * Update processing status and emit to clients
   */
  private updateProcessingStatus(sessionId: string, status: AIProcessingStatus): void {
    this.processingStatus.set(sessionId, status);
    this.io.to(`ai_session_${sessionId}`).emit('processing_status', status);
  }

  /**
   * Verify session access
   */
  private async verifySessionAccess(sessionId: string, userId: string, token: string): Promise<boolean> {
    try {
      // In production, verify JWT token
      // For now, just check if session exists and belongs to user
      const session = await phase3AIService.getSession(sessionId, userId);
      return session !== null;
    } catch (error) {
      logger.error('Failed to verify session access', { error, sessionId, userId });
      return false;
    }
  }

  /**
   * Handle client disconnection
   */
  private handleDisconnection(socketId: string): void {
    // Remove from active connections
    for (const [sessionId, socketIds] of this.activeConnections.entries()) {
      if (socketIds.has(socketId)) {
        socketIds.delete(socketId);
        if (socketIds.size === 0) {
          this.activeConnections.delete(sessionId);
        }
      }
    }
  }

  /**
   * Get active connections count for a session
   */
  public getActiveConnectionsCount(sessionId: string): number {
    return this.activeConnections.get(sessionId)?.size || 0;
  }

  /**
   * Broadcast message to all clients in a session
   */
  public broadcastToSession(sessionId: string, event: string, data: any): void {
    this.io.to(`ai_session_${sessionId}`).emit(event, data);
  }
}

export let realTimeAIService: RealTimeAIService;
