console.log('Testing middleware imports...');

try {
  console.log('Loading security middleware...');
  const security = require('./dist/middleware/security.js');
  console.log('✅ Security middleware loaded');
  console.log('Available exports:', Object.keys(security));
  
  console.log('\nTesting individual middleware functions:');
  
  console.log('- corsMiddleware:', typeof security.corsMiddleware);
  console.log('- helmetMiddleware:', typeof security.helmetMiddleware);
  console.log('- compressionMiddleware:', typeof security.compressionMiddleware);
  console.log('- rateLimitMiddleware:', typeof security.rateLimitMiddleware);
  console.log('- securityHeaders:', typeof security.securityHeaders);
  console.log('- requestId:', typeof security.requestId);
  console.log('- suspiciousActivityDetection:', typeof security.suspiciousActivityDetection);
  
  // Test if they are actually functions
  const middlewares = [
    'corsMiddleware',
    'helmetMiddleware', 
    'compressionMiddleware',
    'rateLimitMiddleware',
    'securityHeaders',
    'requestId',
    'suspiciousActivityDetection'
  ];
  
  console.log('\nChecking if middleware are functions:');
  middlewares.forEach(name => {
    const middleware = security[name];
    console.log(`${name}: ${typeof middleware === 'function' ? '✅ Function' : '❌ Not a function (' + typeof middleware + ')'}`);
  });
  
} catch (error) {
  console.error('❌ Error loading middleware:', error);
}
