"use strict";
/**
 * Async Handler Utility for Freela Syria API
 * Provides robust async error handling for Express routes
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.monitoredAsyncHandler = exports.validatedAsyncHandler = exports.apiAsyncHandler = exports.asyncHandlerWithContext = exports.asyncHandler = void 0;
const errors_1 = require("./errors");
/**
 * Basic async handler that catches promise rejections
 * and forwards them to Express error middleware
 */
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch((error) => {
            // Add request context to AppError instances
            if (error instanceof errors_1.AppError) {
                error.addContext(req);
            }
            next(error);
        });
    };
};
exports.asyncHandler = asyncHandler;
/**
 * Enhanced async handler with additional context tracking
 * Useful for debugging and error monitoring
 */
const asyncHandlerWithContext = (fn, context) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch((error) => {
            // Add request context to AppError instances
            if (error instanceof errors_1.AppError) {
                error.addContext(req);
                // Add additional context if provided
                if (context) {
                    error.details = {
                        ...error.details,
                        context,
                    };
                }
            }
            // For non-AppError instances, create a new AppError with context
            if (!(error instanceof errors_1.AppError)) {
                const contextualError = new errors_1.AppError(error.message || 'Unexpected error occurred', 500, 'INTERNAL', 'HIGH', false, undefined, {
                    originalError: error.name,
                    context,
                    stack: error.stack,
                });
                contextualError.addContext(req);
                next(contextualError);
                return;
            }
            next(error);
        });
    };
};
exports.asyncHandlerWithContext = asyncHandlerWithContext;
/**
 * Async handler specifically for API routes that always return JSON
 * Ensures consistent response format
 */
const apiAsyncHandler = (fn) => {
    return (0, exports.asyncHandler)(async (req, res, next) => {
        try {
            const result = await fn(req, res, next);
            // If response hasn't been sent and we have a result, send it
            if (!res.headersSent && result !== undefined) {
                res.json({
                    success: true,
                    data: result,
                    timestamp: new Date().toISOString(),
                });
            }
        }
        catch (error) {
            throw error; // Re-throw to be caught by asyncHandler
        }
    });
};
exports.apiAsyncHandler = apiAsyncHandler;
/**
 * Async handler with automatic request validation
 * Validates request before executing the handler function
 */
const validatedAsyncHandler = (fn, validator) => {
    return (0, exports.asyncHandler)(async (req, res, next) => {
        // Run validation if provided
        if (validator) {
            await validator(req);
        }
        // Execute the main handler
        return await fn(req, res, next);
    });
};
exports.validatedAsyncHandler = validatedAsyncHandler;
/**
 * Async handler with built-in performance monitoring
 * Logs execution time for performance analysis
 */
const monitoredAsyncHandler = (fn, operationName) => {
    return (0, exports.asyncHandler)(async (req, res, next) => {
        const startTime = Date.now();
        try {
            const result = await fn(req, res, next);
            // Log performance metrics
            const executionTime = Date.now() - startTime;
            console.log(`[PERF] ${operationName || req.path}: ${executionTime}ms`);
            return result;
        }
        catch (error) {
            // Log error with execution time
            const executionTime = Date.now() - startTime;
            console.error(`[ERROR] ${operationName || req.path}: ${executionTime}ms - ${error.message}`);
            throw error;
        }
    });
};
exports.monitoredAsyncHandler = monitoredAsyncHandler;
// Export default as the basic asyncHandler for convenience
exports.default = exports.asyncHandler;
//# sourceMappingURL=asyncHandler.js.map