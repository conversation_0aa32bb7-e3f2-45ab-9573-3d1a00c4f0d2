/**
 * AI-Powered Image Analysis Service for Portfolio Uploads
 * Analyzes images to extract skills, project types, and quality metrics
 * Enhanced with Phase 3 AI integration for real-time conversation processing
 */

import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';
import { openRouterService } from './openRouterService';
import { phase3AIService, ImageAnalysisResult as Phase3ImageResult } from './phase3AIService';

export interface ImageAnalysisResult {
  skills: string[];
  projectType: string;
  qualityScore: number;
  description: string;
  tags: string[];
  suggestedPrice: number;
  confidence: number;
  technicalDetails: {
    software?: string[];
    techniques?: string[];
    style?: string;
    complexity?: 'basic' | 'intermediate' | 'advanced';
  };
}

export interface PortfolioImageData {
  uri: string;
  analysis: ImageAnalysisResult;
  metadata: {
    width: number;
    height: number;
    fileSize: number;
    format: string;
  };
}

export class ImageAnalysisService {
  
  /**
   * Pick image from gallery or camera
   */
  async pickImage(source: 'gallery' | 'camera' = 'gallery'): Promise<string | null> {
    try {
      // Request permissions
      const permission = source === 'camera' 
        ? await ImagePicker.requestCameraPermissionsAsync()
        : await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (permission.status !== 'granted') {
        Alert.alert('خطأ', 'يرجى السماح بالوصول إلى الصور');
        return null;
      }

      // Pick image
      const result = source === 'camera'
        ? await ImagePicker.launchCameraAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.Images,
            allowsEditing: true,
            aspect: [16, 9],
            quality: 0.8,
          })
        : await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.Images,
            allowsEditing: true,
            aspect: [16, 9],
            quality: 0.8,
          });

      if (!result.canceled && result.assets[0]) {
        return result.assets[0].uri;
      }

      return null;
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('خطأ', 'فشل في اختيار الصورة');
      return null;
    }
  }

  /**
   * Analyze portfolio image using AI
   */
  async analyzePortfolioImage(imageUri: string): Promise<ImageAnalysisResult> {
    try {
      // Get image metadata
      const imageInfo = await FileSystem.getInfoAsync(imageUri);
      
      // Convert image to base64
      const imageBase64 = await FileSystem.readAsStringAsync(imageUri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Create AI analysis prompt
      const analysisPrompt = `
تحليل صورة محفظة أعمال لخبير سوري. يرجى تحليل الصورة وتقديم:

1. المهارات المطلوبة لإنتاج هذا العمل
2. نوع المشروع (تصميم جرافيك، برمجة، كتابة، إلخ)
3. تقييم الجودة من 1-10
4. وصف مفصل للعمل
5. كلمات مفتاحية مناسبة
6. سعر مقترح بالدولار الأمريكي للسوق السوري
7. تفاصيل تقنية (برامج، تقنيات، أسلوب)

يرجى الرد بتنسيق JSON:
{
  "skills": ["مهارة1", "مهارة2"],
  "projectType": "نوع المشروع",
  "qualityScore": 8.5,
  "description": "وصف مفصل باللغة العربية",
  "tags": ["كلمة1", "كلمة2"],
  "suggestedPrice": 150,
  "confidence": 0.9,
  "technicalDetails": {
    "software": ["Photoshop", "Illustrator"],
    "techniques": ["تقنية1", "تقنية2"],
    "style": "أسلوب التصميم",
    "complexity": "advanced"
  }
}

الصورة المرفقة: [IMAGE_DATA]
`;

      // Get AI analysis
      const response = await openRouterService.generateResponse(analysisPrompt, {
        model: 'openai/gpt-4-vision-preview',
        temperature: 0.4,
        max_tokens: 1500,
      });

      // Parse response
      const analysis = this.parseAnalysisResponse(response);
      
      // Enhance with Syrian market context
      return this.enhanceWithMarketContext(analysis);

    } catch (error) {
      console.error('Error analyzing image:', error);
      Alert.alert('خطأ', 'فشل في تحليل الصورة');
      throw error;
    }
  }

  /**
   * Analyze multiple portfolio images
   */
  async analyzePortfolioCollection(imageUris: string[]): Promise<PortfolioImageData[]> {
    const results: PortfolioImageData[] = [];

    for (const uri of imageUris) {
      try {
        const analysis = await this.analyzePortfolioImage(uri);
        const metadata = await this.getImageMetadata(uri);
        
        results.push({
          uri,
          analysis,
          metadata,
        });
      } catch (error) {
        console.error(`Error analyzing image ${uri}:`, error);
        // Continue with other images
      }
    }

    return results;
  }

  /**
   * Generate portfolio summary from analyzed images
   */
  async generatePortfolioSummary(portfolioData: PortfolioImageData[]): Promise<{
    overallSkills: string[];
    expertiseLevel: 'beginner' | 'intermediate' | 'expert';
    averageQuality: number;
    recommendedServices: string[];
    suggestedPricing: {
      min: number;
      max: number;
      average: number;
    };
    portfolioStrengths: string[];
    improvementSuggestions: string[];
  }> {
    try {
      // Extract data from all analyses
      const allSkills = portfolioData.flatMap(item => item.analysis.skills);
      const allQualityScores = portfolioData.map(item => item.analysis.qualityScore);
      const allPrices = portfolioData.map(item => item.analysis.suggestedPrice);

      // Calculate metrics
      const skillCounts = this.countSkillFrequency(allSkills);
      const averageQuality = allQualityScores.reduce((a, b) => a + b, 0) / allQualityScores.length;
      const expertiseLevel = this.determineExpertiseLevel(averageQuality, portfolioData.length);

      // Generate AI-powered summary
      const summaryPrompt = `
تحليل محفظة أعمال شاملة لخبير سوري. البيانات:
- المهارات: ${JSON.stringify(skillCounts)}
- متوسط الجودة: ${averageQuality}
- عدد الأعمال: ${portfolioData.length}

يرجى تقديم:
1. أهم المهارات
2. مستوى الخبرة
3. الخدمات المقترحة
4. نطاق الأسعار
5. نقاط القوة
6. اقتراحات للتحسين

الرد بتنسيق JSON مع النصوص باللغة العربية.
`;

      const response = await openRouterService.generateResponse(summaryPrompt, {
        model: 'openai/gpt-4-turbo-preview',
        temperature: 0.6,
        max_tokens: 1000,
      });

      const aiSummary = this.parseSummaryResponse(response);

      return {
        overallSkills: Object.keys(skillCounts).slice(0, 10),
        expertiseLevel,
        averageQuality,
        recommendedServices: aiSummary.recommendedServices || [],
        suggestedPricing: {
          min: Math.min(...allPrices),
          max: Math.max(...allPrices),
          average: allPrices.reduce((a, b) => a + b, 0) / allPrices.length,
        },
        portfolioStrengths: aiSummary.portfolioStrengths || [],
        improvementSuggestions: aiSummary.improvementSuggestions || [],
      };

    } catch (error) {
      console.error('Error generating portfolio summary:', error);
      throw error;
    }
  }

  /**
   * Parse AI analysis response
   */
  private parseAnalysisResponse(response: string): ImageAnalysisResult {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          skills: parsed.skills || [],
          projectType: parsed.projectType || 'غير محدد',
          qualityScore: parsed.qualityScore || 5,
          description: parsed.description || '',
          tags: parsed.tags || [],
          suggestedPrice: parsed.suggestedPrice || 50,
          confidence: parsed.confidence || 0.7,
          technicalDetails: parsed.technicalDetails || {},
        };
      }

      // Fallback
      return this.createFallbackAnalysis(response);
    } catch (error) {
      console.error('Error parsing analysis response:', error);
      return this.createFallbackAnalysis(response);
    }
  }

  /**
   * Enhance analysis with Syrian market context
   */
  private enhanceWithMarketContext(analysis: ImageAnalysisResult): ImageAnalysisResult {
    // Adjust pricing for Syrian market
    const marketMultiplier = 0.7; // Adjust for local market
    analysis.suggestedPrice = Math.round(analysis.suggestedPrice * marketMultiplier);

    // Add Syrian-specific tags
    const syrianTags = ['سوريا', 'عربي', 'محلي'];
    analysis.tags = [...analysis.tags, ...syrianTags];

    return analysis;
  }

  /**
   * Get image metadata
   */
  private async getImageMetadata(uri: string): Promise<{
    width: number;
    height: number;
    fileSize: number;
    format: string;
  }> {
    try {
      const info = await FileSystem.getInfoAsync(uri);
      return {
        width: 0, // Would need additional library for dimensions
        height: 0,
        fileSize: info.size || 0,
        format: uri.split('.').pop() || 'unknown',
      };
    } catch (error) {
      return {
        width: 0,
        height: 0,
        fileSize: 0,
        format: 'unknown',
      };
    }
  }

  /**
   * Count skill frequency
   */
  private countSkillFrequency(skills: string[]): Record<string, number> {
    return skills.reduce((acc, skill) => {
      acc[skill] = (acc[skill] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  /**
   * Determine expertise level
   */
  private determineExpertiseLevel(averageQuality: number, portfolioSize: number): 'beginner' | 'intermediate' | 'expert' {
    if (averageQuality >= 8 && portfolioSize >= 10) return 'expert';
    if (averageQuality >= 6 && portfolioSize >= 5) return 'intermediate';
    return 'beginner';
  }

  /**
   * Create fallback analysis
   */
  private createFallbackAnalysis(response: string): ImageAnalysisResult {
    return {
      skills: ['تصميم عام'],
      projectType: 'عمل إبداعي',
      qualityScore: 6,
      description: response.substring(0, 200),
      tags: ['إبداع', 'تصميم'],
      suggestedPrice: 75,
      confidence: 0.5,
      technicalDetails: {
        complexity: 'intermediate',
      },
    };
  }

  /**
   * Analyze image with Phase 3 AI integration
   */
  async analyzeImageWithAI(
    sessionId: string,
    imageUri: string,
    analysisType: 'portfolio' | 'work_sample' | 'skill_demonstration' | 'certificate' = 'portfolio'
  ): Promise<Phase3ImageResult> {
    try {
      console.log('🖼️ Analyzing image with Phase 3 AI:', { sessionId, analysisType });

      // Use Phase 3 AI service for enhanced image analysis
      const result = await phase3AIService.analyzeImage(sessionId, imageUri, analysisType);

      console.log('✅ Image analyzed with AI:', {
        analysisType: result.analysisType,
        skillsCount: result.extractedSkills.length,
        confidence: result.confidence
      });

      return result;
    } catch (error) {
      console.error('❌ Failed to analyze image with AI:', error);
      throw error;
    }
  }

  /**
   * Pick and analyze image with Phase 3 AI in one step
   */
  async pickAndAnalyzeWithAI(
    sessionId: string,
    source: 'gallery' | 'camera' = 'gallery',
    analysisType: 'portfolio' | 'work_sample' | 'skill_demonstration' | 'certificate' = 'portfolio'
  ): Promise<Phase3ImageResult | null> {
    try {
      console.log('📸 Pick and analyze with AI:', { sessionId, source, analysisType });

      // Pick image
      const imageUri = await this.pickImage(source);
      if (!imageUri) {
        return null;
      }

      // Analyze with Phase 3 AI
      const result = await this.analyzeImageWithAI(sessionId, imageUri, analysisType);

      return result;
    } catch (error) {
      console.error('❌ Error in pick and analyze with AI:', error);
      throw error;
    }
  }

  /**
   * Enhanced portfolio analysis with Syrian market context
   */
  async analyzePortfolioWithMarketContext(
    sessionId: string,
    imageUri: string
  ): Promise<{
    analysis: Phase3ImageResult;
    marketInsights: {
      demandLevel: 'high' | 'medium' | 'low';
      competitionLevel: 'high' | 'medium' | 'low';
      pricingRecommendation: {
        min: number;
        max: number;
        recommended: number;
      };
      marketTrends: string[];
      targetAudience: string[];
    };
    recommendations: string[];
  }> {
    try {
      console.log('📊 Analyzing portfolio with market context:', { sessionId });

      // Get AI analysis
      const analysis = await this.analyzeImageWithAI(sessionId, imageUri, 'portfolio');

      // Generate market insights using AI
      const marketPrompt = `
تحليل السوق السوري للمهارات التالية: ${analysis.extractedSkills.join(', ')}

يرجى تقديم:
1. مستوى الطلب في السوق السوري
2. مستوى المنافسة
3. توصيات الأسعار (بالدولار الأمريكي)
4. اتجاهات السوق الحالية
5. الجمهور المستهدف

الرد بتنسيق JSON:
{
  "demandLevel": "high",
  "competitionLevel": "medium",
  "pricingRecommendation": {
    "min": 50,
    "max": 200,
    "recommended": 100
  },
  "marketTrends": ["اتجاه1", "اتجاه2"],
  "targetAudience": ["جمهور1", "جمهور2"]
}
`;

      const marketResponse = await openRouterService.generateResponse(marketPrompt, {
        model: 'openai/gpt-4-turbo-preview',
        temperature: 0.6,
        max_tokens: 800,
      });

      const marketInsights = this.parseMarketInsights(marketResponse);

      // Generate recommendations
      const recommendations = this.generateRecommendations(analysis, marketInsights);

      return {
        analysis,
        marketInsights,
        recommendations
      };
    } catch (error) {
      console.error('❌ Error analyzing portfolio with market context:', error);
      throw error;
    }
  }

  /**
   * Parse market insights from AI response
   */
  private parseMarketInsights(response: string): any {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      // Fallback
      return {
        demandLevel: 'medium',
        competitionLevel: 'medium',
        pricingRecommendation: {
          min: 50,
          max: 150,
          recommended: 100
        },
        marketTrends: ['التحول الرقمي', 'العمل عن بُعد'],
        targetAudience: ['الشركات الناشئة', 'المؤسسات الصغيرة']
      };
    } catch (error) {
      console.error('Error parsing market insights:', error);
      return {};
    }
  }

  /**
   * Generate recommendations based on analysis and market insights
   */
  private generateRecommendations(
    analysis: Phase3ImageResult,
    marketInsights: any
  ): string[] {
    const recommendations: string[] = [];

    // Skill-based recommendations
    if (analysis.extractedSkills.length > 0) {
      recommendations.push(`ركز على تطوير مهاراتك في: ${analysis.extractedSkills.slice(0, 3).join(', ')}`);
    }

    // Market-based recommendations
    if (marketInsights.demandLevel === 'high') {
      recommendations.push('هناك طلب عالي على هذه المهارات في السوق السوري');
    }

    if (marketInsights.competitionLevel === 'low') {
      recommendations.push('المنافسة منخفضة، فرصة جيدة للدخول في هذا المجال');
    }

    // Pricing recommendations
    if (marketInsights.pricingRecommendation) {
      recommendations.push(
        `السعر المقترح: ${marketInsights.pricingRecommendation.recommended} دولار`
      );
    }

    return recommendations;
  }

  /**
   * Parse summary response
   */
  private parseSummaryResponse(response: string): any {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      return jsonMatch ? JSON.parse(jsonMatch[0]) : {};
    } catch (error) {
      return {};
    }
  }
}

// Export singleton instance
export const imageAnalysisService = new ImageAnalysisService();
