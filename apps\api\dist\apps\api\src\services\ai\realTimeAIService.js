"use strict";
/**
 * Real-time AI Service for Phase 3
 * WebSocket-based real-time AI conversation management
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.realTimeAIService = exports.RealTimeAIService = void 0;
const supabase_1 = require("@freela/database/src/supabase");
const phase3AIService_1 = require("./phase3AIService");
const logger_1 = require("../../utils/logger");
class RealTimeAIService {
    io;
    activeConnections = new Map(); // sessionId -> Set of socketIds
    userSessions = new Map(); // userId -> Set of sessionIds
    processingStatus = new Map(); // sessionId -> status
    constructor(io) {
        this.io = io;
        this.setupSocketHandlers();
        this.setupSupabaseRealTime();
    }
    /**
     * Setup Socket.IO event handlers
     */
    setupSocketHandlers() {
        this.io.on('connection', (socket) => {
            logger_1.logger.info('Client connected to real-time AI service', { socketId: socket.id });
            // Join AI conversation room
            socket.on('join_ai_session', async (data) => {
                try {
                    // Verify user authentication and session ownership
                    const isValid = await this.verifySessionAccess(data.sessionId, data.userId, data.token);
                    if (!isValid) {
                        socket.emit('error', { message: 'Unauthorized access to AI session' });
                        return;
                    }
                    // Join session room
                    socket.join(`ai_session_${data.sessionId}`);
                    // Track connection
                    if (!this.activeConnections.has(data.sessionId)) {
                        this.activeConnections.set(data.sessionId, new Set());
                    }
                    this.activeConnections.get(data.sessionId).add(socket.id);
                    // Track user sessions
                    if (!this.userSessions.has(data.userId)) {
                        this.userSessions.set(data.userId, new Set());
                    }
                    this.userSessions.get(data.userId).add(data.sessionId);
                    socket.emit('joined_ai_session', {
                        sessionId: data.sessionId,
                        status: 'connected',
                        features: {
                            realTimeProcessing: true,
                            typingIndicators: true,
                            progressTracking: true,
                            instantAnalysis: true
                        }
                    });
                    logger_1.logger.info('Client joined AI session', {
                        socketId: socket.id,
                        sessionId: data.sessionId,
                        userId: data.userId
                    });
                }
                catch (error) {
                    logger_1.logger.error('Failed to join AI session', { error, socketId: socket.id });
                    socket.emit('error', { message: 'Failed to join AI session' });
                }
            });
            // Handle real-time message sending
            socket.on('send_ai_message', async (data) => {
                try {
                    // Emit processing status
                    this.updateProcessingStatus(data.sessionId, {
                        sessionId: data.sessionId,
                        status: 'processing',
                        progress: 10,
                        currentTask: 'تحليل الرسالة...'
                    });
                    // Process message with Phase 3 AI service
                    const result = await phase3AIService_1.phase3AIService.processMessage(data.sessionId, data.userId, data.message, data.messageType || 'text', data.metadata || {});
                    // Update processing status
                    this.updateProcessingStatus(data.sessionId, {
                        sessionId: data.sessionId,
                        status: 'complete',
                        progress: 100,
                        currentTask: 'تم الانتهاء'
                    });
                    // Emit AI response to all clients in the session
                    this.io.to(`ai_session_${data.sessionId}`).emit('ai_response', {
                        sessionId: data.sessionId,
                        userMessage: {
                            content: data.message,
                            type: data.messageType || 'text',
                            timestamp: new Date()
                        },
                        aiResponse: {
                            content: result.aiResponse,
                            timestamp: new Date(),
                            extractedData: result.extractedData
                        },
                        sessionProgress: {
                            currentStep: result.nextStep,
                            completionRate: result.completionRate,
                            isCompleted: result.completionRate >= 100
                        }
                    });
                    logger_1.logger.info('Real-time AI message processed', {
                        sessionId: data.sessionId,
                        userId: data.userId,
                        messageLength: data.message.length,
                        nextStep: result.nextStep
                    });
                }
                catch (error) {
                    logger_1.logger.error('Failed to process real-time AI message', { error, sessionId: data.sessionId });
                    socket.emit('ai_error', {
                        sessionId: data.sessionId,
                        message: 'فشل في معالجة الرسالة. يرجى المحاولة مرة أخرى.',
                        error: error?.message || 'Unknown error'
                    });
                }
            });
            // Handle typing indicators
            socket.on('typing_start', (data) => {
                socket.to(`ai_session_${data.sessionId}`).emit('user_typing', {
                    sessionId: data.sessionId,
                    userId: data.userId,
                    isTyping: true,
                    timestamp: new Date()
                });
            });
            socket.on('typing_stop', (data) => {
                socket.to(`ai_session_${data.sessionId}`).emit('user_typing', {
                    sessionId: data.sessionId,
                    userId: data.userId,
                    isTyping: false,
                    timestamp: new Date()
                });
            });
            // Handle voice message processing
            socket.on('process_voice_message', async (data) => {
                try {
                    this.updateProcessingStatus(data.sessionId, {
                        sessionId: data.sessionId,
                        status: 'analyzing',
                        progress: 20,
                        currentTask: 'تحليل الرسالة الصوتية...'
                    });
                    // For now, use provided transcription or simulate
                    const transcription = data.transcription || 'تم تحويل الرسالة الصوتية إلى نص';
                    // Process as voice message
                    const result = await phase3AIService_1.phase3AIService.processMessage(data.sessionId, data.userId, transcription, 'voice_transcript', {
                        voiceData: {
                            dialect: data.dialect || 'general',
                            originalAudio: true,
                            transcriptionMethod: data.transcription ? 'provided' : 'simulated'
                        }
                    });
                    this.updateProcessingStatus(data.sessionId, {
                        sessionId: data.sessionId,
                        status: 'complete',
                        progress: 100
                    });
                    // Emit voice processing result
                    this.io.to(`ai_session_${data.sessionId}`).emit('voice_processed', {
                        sessionId: data.sessionId,
                        transcription,
                        aiResponse: result.aiResponse,
                        extractedData: result.extractedData,
                        sessionProgress: {
                            currentStep: result.nextStep,
                            completionRate: result.completionRate
                        }
                    });
                }
                catch (error) {
                    logger_1.logger.error('Failed to process voice message', { error, sessionId: data.sessionId });
                    socket.emit('voice_error', {
                        sessionId: data.sessionId,
                        message: 'فشل في معالجة الرسالة الصوتية'
                    });
                }
            });
            // Handle image analysis
            socket.on('analyze_image', async (data) => {
                try {
                    this.updateProcessingStatus(data.sessionId, {
                        sessionId: data.sessionId,
                        status: 'analyzing',
                        progress: 30,
                        currentTask: 'تحليل الصورة...'
                    });
                    // Simulate image analysis (in production, integrate with GPT-4 Vision)
                    const analysisResult = `تم تحليل الصورة (${data.analysisType || 'portfolio'}). ${data.description || ''}
          
نتائج التحليل:
- نوع المحتوى: عمل إبداعي
- الجودة: عالية
- المهارات المستنتجة: تصميم، إبداع، احترافية`;
                    // Process as image message
                    const result = await phase3AIService_1.phase3AIService.processMessage(data.sessionId, data.userId, analysisResult, 'image_description', {
                        imageData: {
                            analysisType: data.analysisType || 'portfolio',
                            description: data.description || '',
                            hasImage: true
                        }
                    });
                    this.updateProcessingStatus(data.sessionId, {
                        sessionId: data.sessionId,
                        status: 'complete',
                        progress: 100
                    });
                    // Emit image analysis result
                    this.io.to(`ai_session_${data.sessionId}`).emit('image_analyzed', {
                        sessionId: data.sessionId,
                        analysis: analysisResult,
                        aiResponse: result.aiResponse,
                        extractedData: result.extractedData,
                        sessionProgress: {
                            currentStep: result.nextStep,
                            completionRate: result.completionRate
                        }
                    });
                }
                catch (error) {
                    logger_1.logger.error('Failed to analyze image', { error, sessionId: data.sessionId });
                    socket.emit('image_error', {
                        sessionId: data.sessionId,
                        message: 'فشل في تحليل الصورة'
                    });
                }
            });
            // Handle disconnection
            socket.on('disconnect', () => {
                this.handleDisconnection(socket.id);
                logger_1.logger.info('Client disconnected from real-time AI service', { socketId: socket.id });
            });
        });
    }
    /**
     * Setup Supabase real-time subscriptions
     */
    setupSupabaseRealTime() {
        // Subscribe to AI chat messages
        supabase_1.supabase
            .channel('ai_chat_messages')
            .on('postgres_changes', { event: 'INSERT', schema: 'public', table: 'ai_chat_messages' }, (payload) => {
            const message = payload.new;
            this.io.to(`ai_session_${message.session_id}`).emit('new_ai_message', {
                id: message.id,
                sessionId: message.session_id,
                role: message.role,
                content: message.content,
                messageType: message.message_type,
                timestamp: message.created_at,
                extractedData: message.extracted_data
            });
        })
            .subscribe();
        // Subscribe to session updates
        supabase_1.supabase
            .channel('ai_chat_sessions')
            .on('postgres_changes', { event: 'UPDATE', schema: 'public', table: 'ai_chat_sessions' }, (payload) => {
            const session = payload.new;
            this.io.to(`ai_session_${session.id}`).emit('session_updated', {
                sessionId: session.id,
                currentStep: session.current_step,
                completionRate: session.completion_rate,
                status: session.status,
                extractedData: session.extracted_data
            });
        })
            .subscribe();
    }
    /**
     * Update processing status and emit to clients
     */
    updateProcessingStatus(sessionId, status) {
        this.processingStatus.set(sessionId, status);
        this.io.to(`ai_session_${sessionId}`).emit('processing_status', status);
    }
    /**
     * Verify session access
     */
    async verifySessionAccess(sessionId, userId, token) {
        try {
            // In production, verify JWT token
            // For now, just check if session exists and belongs to user
            const session = await phase3AIService_1.phase3AIService.getSession(sessionId, userId);
            return session !== null;
        }
        catch (error) {
            logger_1.logger.error('Failed to verify session access', { error, sessionId, userId });
            return false;
        }
    }
    /**
     * Handle client disconnection
     */
    handleDisconnection(socketId) {
        // Remove from active connections
        for (const [sessionId, socketIds] of this.activeConnections.entries()) {
            if (socketIds.has(socketId)) {
                socketIds.delete(socketId);
                if (socketIds.size === 0) {
                    this.activeConnections.delete(sessionId);
                }
            }
        }
    }
    /**
     * Get active connections count for a session
     */
    getActiveConnectionsCount(sessionId) {
        return this.activeConnections.get(sessionId)?.size || 0;
    }
    /**
     * Broadcast message to all clients in a session
     */
    broadcastToSession(sessionId, event, data) {
        this.io.to(`ai_session_${sessionId}`).emit(event, data);
    }
}
exports.RealTimeAIService = RealTimeAIService;
//# sourceMappingURL=realTimeAIService.js.map