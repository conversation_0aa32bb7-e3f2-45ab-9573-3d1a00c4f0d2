console.log('Testing step by step...');

try {
  const express = require('express');
  const security = require('./dist/middleware/security.js');
  const { requestLogger } = require('./dist/utils/logger.js');
  
  console.log('Creating express app...');
  const app = express();
  
  console.log('Setting trust proxy...');
  app.set('trust proxy', 1);
  
  const middlewares = [
    { name: 'requestId', middleware: security.default.requestId },
    { name: 'securityHeaders', middleware: security.default.securityHeaders },
    { name: 'helmet', middleware: security.default.helmet },
    { name: 'cors', middleware: security.default.cors },
    { name: 'compression', middleware: security.default.compression },
    { name: 'suspiciousActivityDetection', middleware: security.default.suspiciousActivityDetection },
    { name: 'rateLimit', middleware: security.default.rateLimit },
    { name: 'express.json', middleware: express.json({ limit: '10mb' }) },
    { name: 'express.urlencoded', middleware: express.urlencoded({ extended: true, limit: '10mb' }) },
    { name: 'requestLogger', middleware: requestLogger }
  ];
  
  console.log('\nTesting each middleware step by step:');
  
  for (const { name, middleware } of middlewares) {
    try {
      console.log(`Adding ${name}...`);
      console.log(`  Type: ${typeof middleware}`);
      console.log(`  Is function: ${typeof middleware === 'function'}`);
      
      if (typeof middleware !== 'function') {
        console.log(`❌ ${name} - Not a function!`);
        console.log(`  Value:`, middleware);
        break;
      }
      
      app.use(middleware);
      console.log(`✅ ${name} - Successfully added`);
    } catch (error) {
      console.log(`❌ ${name} - Error:`, error.message);
      break;
    }
  }
  
  console.log('\n✅ All middleware tests completed');
  
} catch (error) {
  console.error('❌ Error during testing:', error);
}
