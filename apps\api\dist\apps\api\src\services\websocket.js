"use strict";
/**
 * WebSocket Service for Real-time AI Chat
 * Handles real-time communication for AI onboarding conversations
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketService = void 0;
const socket_io_1 = require("socket.io");
const logger_1 = require("../utils/logger");
const aiConversation_1 = require("./aiConversation");
const jwt_1 = require("../utils/jwt");
class WebSocketService {
    io;
    connectedUsers = new Map();
    constructor(server) {
        this.io = new socket_io_1.Server(server, {
            cors: {
                origin: process.env.FRONTEND_URLS?.split(',') || ['http://localhost:3000'],
                methods: ['GET', 'POST'],
                credentials: true,
            },
            transports: ['websocket', 'polling'],
        });
        this.setupMiddleware();
        this.setupEventHandlers();
    }
    /**
     * Setup authentication middleware
     */
    setupMiddleware() {
        this.io.use(async (socket, next) => {
            try {
                const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
                if (!token) {
                    logger_1.logger.warn('WebSocket connection attempted without token', {
                        socketId: socket.id,
                        ip: socket.handshake.address,
                    });
                    return next(new Error('Authentication required'));
                }
                // Verify JWT token
                const payload = await jwt_1.jwtUtils.verifyAccessToken(token);
                if (!payload) {
                    logger_1.logger.warn('WebSocket connection with invalid token', {
                        socketId: socket.id,
                        token: token.substring(0, 20) + '...',
                    });
                    return next(new Error('Invalid token'));
                }
                // Attach user info to socket
                socket.userId = payload.userId;
                socket.userRole = payload.role;
                logger_1.logger.info('WebSocket connection authenticated', {
                    socketId: socket.id,
                    userId: socket.userId,
                    userRole: socket.userRole,
                });
                next();
            }
            catch (error) {
                logger_1.logger.error('WebSocket authentication error', {
                    socketId: socket.id,
                    error: error.message,
                });
                next(new Error('Authentication failed'));
            }
        });
    }
    /**
     * Setup event handlers
     */
    setupEventHandlers() {
        this.io.on('connection', (socket) => {
            this.handleConnection(socket);
        });
    }
    /**
     * Handle new socket connection
     */
    handleConnection(socket) {
        const { userId, userRole } = socket;
        if (!userId) {
            socket.disconnect();
            return;
        }
        // Store connected user
        this.connectedUsers.set(userId, socket);
        logger_1.logger.info('User connected to WebSocket', {
            socketId: socket.id,
            userId,
            userRole,
            totalConnections: this.connectedUsers.size,
        });
        // Join user to their personal room
        socket.join(`user:${userId}`);
        // Setup event handlers for this socket
        this.setupSocketEventHandlers(socket);
        // Handle disconnection
        socket.on('disconnect', (reason) => {
            this.handleDisconnection(socket, reason);
        });
    }
    /**
     * Setup event handlers for individual socket
     */
    setupSocketEventHandlers(socket) {
        // Join AI conversation session
        socket.on('join_ai_session', async (data) => {
            try {
                const { sessionId } = data;
                const session = aiConversation_1.aiConversationService.getSession(sessionId);
                if (!session || session.userId !== socket.userId) {
                    socket.emit('error', { message: 'Session not found or access denied' });
                    return;
                }
                socket.sessionId = sessionId;
                socket.join(`session:${sessionId}`);
                // Send session data
                socket.emit('session_joined', {
                    sessionId,
                    currentStep: session.currentStep,
                    messages: session.messages,
                    extractedData: session.extractedData,
                });
                logger_1.logger.info('User joined AI session', {
                    userId: socket.userId,
                    sessionId,
                    currentStep: session.currentStep,
                });
            }
            catch (error) {
                logger_1.logger.error('Error joining AI session', {
                    userId: socket.userId,
                    error: error.message,
                });
                socket.emit('error', { message: 'Failed to join session' });
            }
        });
        // Send message to AI
        socket.on('send_ai_message', async (data) => {
            try {
                const { sessionId, message } = data;
                if (!message || message.trim().length === 0) {
                    socket.emit('error', { message: 'Message cannot be empty' });
                    return;
                }
                // Verify session access
                const session = aiConversation_1.aiConversationService.getSession(sessionId);
                if (!session || session.userId !== socket.userId) {
                    socket.emit('error', { message: 'Session not found or access denied' });
                    return;
                }
                // Show typing indicator
                socket.to(`session:${sessionId}`).emit('ai_typing', { isTyping: true });
                // Process message with AI
                const result = await aiConversation_1.aiConversationService.processMessage(sessionId, message);
                // Hide typing indicator
                socket.to(`session:${sessionId}`).emit('ai_typing', { isTyping: false });
                // Send AI response
                const response = {
                    sessionId,
                    userMessage: {
                        id: `msg_${Date.now()}_user`,
                        content: message,
                        type: 'user',
                        timestamp: new Date(),
                    },
                    aiMessage: {
                        id: `msg_${Date.now()}_ai`,
                        content: result.aiResponse.content,
                        type: 'ai',
                        timestamp: result.aiResponse.timestamp,
                    },
                    currentStep: result.session.currentStep,
                    extractedData: result.session.extractedData,
                    isCompleted: result.session.status === 'completed',
                };
                // Send to user
                socket.emit('ai_message_response', response);
                // Also send to session room (for potential observers)
                socket.to(`session:${sessionId}`).emit('ai_message_response', response);
                logger_1.logger.info('AI message processed via WebSocket', {
                    userId: socket.userId,
                    sessionId,
                    messageLength: message.length,
                    currentStep: result.session.currentStep,
                });
            }
            catch (error) {
                logger_1.logger.error('Error processing AI message via WebSocket', {
                    userId: socket.userId,
                    sessionId: data.sessionId,
                    error: error.message,
                });
                socket.emit('error', {
                    message: 'Failed to process message',
                    details: error.message
                });
            }
        });
        // Handle typing indicators
        socket.on('typing', (data) => {
            const { sessionId, isTyping } = data;
            socket.to(`session:${sessionId}`).emit('user_typing', {
                userId: socket.userId,
                isTyping,
                timestamp: new Date(),
            });
        });
        // Start new AI conversation
        socket.on('start_ai_conversation', async (data) => {
            try {
                const { userRole, language, sessionType = 'onboarding' } = data;
                if (!socket.userId) {
                    socket.emit('error', { message: 'User not authenticated' });
                    return;
                }
                // Start new conversation
                const session = await aiConversation_1.aiConversationService.startConversation({
                    userId: socket.userId,
                    userRole,
                    language,
                    sessionType,
                });
                // Join session room
                socket.sessionId = session.id;
                socket.join(`session:${session.id}`);
                // Send session data
                socket.emit('conversation_started', {
                    sessionId: session.id,
                    currentStep: session.currentStep,
                    messages: session.messages,
                    extractedData: session.extractedData,
                });
                logger_1.logger.info('New AI conversation started via WebSocket', {
                    userId: socket.userId,
                    sessionId: session.id,
                    userRole,
                    language,
                    sessionType,
                });
            }
            catch (error) {
                logger_1.logger.error('Error starting AI conversation via WebSocket', {
                    userId: socket.userId,
                    error: error.message,
                });
                socket.emit('error', {
                    message: 'Failed to start conversation',
                    details: error.message
                });
            }
        });
        // Get user's conversation sessions
        socket.on('get_user_sessions', () => {
            try {
                if (!socket.userId) {
                    socket.emit('error', { message: 'User not authenticated' });
                    return;
                }
                const sessions = aiConversation_1.aiConversationService.getUserSessions(socket.userId);
                socket.emit('user_sessions', { sessions });
            }
            catch (error) {
                logger_1.logger.error('Error getting user sessions via WebSocket', {
                    userId: socket.userId,
                    error: error.message,
                });
                socket.emit('error', {
                    message: 'Failed to get sessions',
                    details: error.message
                });
            }
        });
    }
    /**
     * Handle socket disconnection
     */
    handleDisconnection(socket, reason) {
        const { userId } = socket;
        if (userId) {
            this.connectedUsers.delete(userId);
        }
        logger_1.logger.info('User disconnected from WebSocket', {
            socketId: socket.id,
            userId,
            reason,
            totalConnections: this.connectedUsers.size,
        });
    }
    /**
     * Send message to specific user
     */
    sendToUser(userId, event, data) {
        this.io.to(`user:${userId}`).emit(event, data);
    }
    /**
     * Send message to session participants
     */
    sendToSession(sessionId, event, data) {
        this.io.to(`session:${sessionId}`).emit(event, data);
    }
    /**
     * Get connected users count
     */
    getConnectedUsersCount() {
        return this.connectedUsers.size;
    }
    /**
     * Check if user is connected
     */
    isUserConnected(userId) {
        return this.connectedUsers.has(userId);
    }
}
exports.WebSocketService = WebSocketService;
//# sourceMappingURL=websocket.js.map