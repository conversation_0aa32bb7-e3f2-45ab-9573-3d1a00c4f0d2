"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validationHelpers = exports.commonSchemas = exports.validateMultiple = exports.validateParams = exports.validateQuery = exports.validateRequest = exports.validateBody = exports.validate = void 0;
const zod_1 = require("zod");
const logger_1 = require("../utils/logger");
/**
 * Generic validation middleware factory
 */
const validate = (schema, source = 'body') => {
    return (req, res, next) => {
        try {
            const dataToValidate = req[source];
            const result = schema.safeParse(dataToValidate);
            if (!result.success) {
                const errors = formatZodErrors(result.error);
                logger_1.logger.warn('Validation failed', {
                    source,
                    errors,
                    data: dataToValidate,
                    endpoint: req.path,
                    method: req.method,
                });
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    code: 'VALIDATION_ERROR',
                    errors,
                });
            }
            // Replace the original data with validated and transformed data
            req[source] = result.data;
            next();
        }
        catch (error) {
            logger_1.logger.error('Validation middleware error', { error, source });
            return res.status(500).json({
                success: false,
                message: 'Validation processing failed',
                code: 'VALIDATION_PROCESSING_ERROR',
            });
        }
    };
};
exports.validate = validate;
/**
 * Format Zod errors into a more user-friendly format
 */
const formatZodErrors = (error) => {
    return error.errors.map((err) => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
    }));
};
/**
 * Validate request body
 */
const validateBody = (schema) => (0, exports.validate)(schema, 'body');
exports.validateBody = validateBody;
/**
 * Alias for validateBody for backward compatibility
 */
exports.validateRequest = exports.validateBody;
/**
 * Validate query parameters
 */
const validateQuery = (schema) => (0, exports.validate)(schema, 'query');
exports.validateQuery = validateQuery;
/**
 * Validate URL parameters
 */
const validateParams = (schema) => (0, exports.validate)(schema, 'params');
exports.validateParams = validateParams;
/**
 * Validate multiple sources at once
 */
const validateMultiple = (schemas) => {
    return (req, res, next) => {
        const errors = [];
        // Validate body
        if (schemas.body) {
            const bodyResult = schemas.body.safeParse(req.body);
            if (!bodyResult.success) {
                const bodyErrors = formatZodErrors(bodyResult.error).map(err => ({
                    ...err,
                    source: 'body',
                }));
                errors.push(...bodyErrors);
            }
            else {
                req.body = bodyResult.data;
            }
        }
        // Validate query
        if (schemas.query) {
            const queryResult = schemas.query.safeParse(req.query);
            if (!queryResult.success) {
                const queryErrors = formatZodErrors(queryResult.error).map(err => ({
                    ...err,
                    source: 'query',
                }));
                errors.push(...queryErrors);
            }
            else {
                req.query = queryResult.data;
            }
        }
        // Validate params
        if (schemas.params) {
            const paramsResult = schemas.params.safeParse(req.params);
            if (!paramsResult.success) {
                const paramsErrors = formatZodErrors(paramsResult.error).map(err => ({
                    ...err,
                    source: 'params',
                }));
                errors.push(...paramsErrors);
            }
            else {
                req.params = paramsResult.data;
            }
        }
        if (errors.length > 0) {
            logger_1.logger.warn('Multiple validation failed', {
                errors,
                endpoint: req.path,
                method: req.method,
            });
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                code: 'VALIDATION_ERROR',
                errors,
            });
        }
        next();
    };
};
exports.validateMultiple = validateMultiple;
// Common validation schemas
exports.commonSchemas = {
    // Pagination
    pagination: zod_1.z.object({
        page: zod_1.z.string().transform(Number).pipe(zod_1.z.number().min(1)).default('1'),
        limit: zod_1.z.string().transform(Number).pipe(zod_1.z.number().min(1).max(100)).default('10'),
        sortBy: zod_1.z.string().optional(),
        sortOrder: zod_1.z.enum(['asc', 'desc']).default('desc'),
    }),
    // ID parameter
    idParam: zod_1.z.object({
        id: zod_1.z.string().min(1, 'ID is required'),
    }),
    // Search query
    search: zod_1.z.object({
        q: zod_1.z.string().min(1, 'Search query is required').max(100, 'Search query too long'),
        category: zod_1.z.string().optional(),
        tags: zod_1.z.string().optional(),
        minPrice: zod_1.z.string().transform(Number).pipe(zod_1.z.number().min(0)).optional(),
        maxPrice: zod_1.z.string().transform(Number).pipe(zod_1.z.number().min(0)).optional(),
        location: zod_1.z.string().optional(),
        sortBy: zod_1.z.enum(['relevance', 'price', 'rating', 'date']).default('relevance'),
        sortOrder: zod_1.z.enum(['asc', 'desc']).default('desc'),
    }),
    // File upload
    fileUpload: zod_1.z.object({
        filename: zod_1.z.string().min(1, 'Filename is required'),
        mimeType: zod_1.z.string().min(1, 'MIME type is required'),
        size: zod_1.z.number().max(10 * 1024 * 1024, 'File size must be less than 10MB'),
    }),
    // Language preference
    language: zod_1.z.object({
        lang: zod_1.z.enum(['ar', 'en']).default('ar'),
    }),
    // Location data
    location: zod_1.z.object({
        governorate: zod_1.z.string().min(1, 'Governorate is required'),
        city: zod_1.z.string().min(1, 'City is required'),
        district: zod_1.z.string().optional(),
        coordinates: zod_1.z.object({
            latitude: zod_1.z.number().min(-90).max(90),
            longitude: zod_1.z.number().min(-180).max(180),
        }).optional(),
    }),
    // Date range
    dateRange: zod_1.z.object({
        startDate: zod_1.z.string().datetime().optional(),
        endDate: zod_1.z.string().datetime().optional(),
    }).refine((data) => {
        if (data.startDate && data.endDate) {
            return new Date(data.startDate) <= new Date(data.endDate);
        }
        return true;
    }, {
        message: 'Start date must be before end date',
        path: ['endDate'],
    }),
    // Price range
    priceRange: zod_1.z.object({
        minPrice: zod_1.z.number().min(0, 'Minimum price cannot be negative').optional(),
        maxPrice: zod_1.z.number().min(0, 'Maximum price cannot be negative').optional(),
        currency: zod_1.z.enum(['USD', 'SYP']).default('USD'),
    }).refine((data) => {
        if (data.minPrice !== undefined && data.maxPrice !== undefined) {
            return data.minPrice <= data.maxPrice;
        }
        return true;
    }, {
        message: 'Minimum price must be less than or equal to maximum price',
        path: ['maxPrice'],
    }),
};
// Validation helpers
exports.validationHelpers = {
    /**
     * Create a validation middleware for pagination
     */
    pagination: () => (0, exports.validateQuery)(exports.commonSchemas.pagination),
    /**
     * Create a validation middleware for ID parameter
     */
    idParam: () => (0, exports.validateParams)(exports.commonSchemas.idParam),
    /**
     * Create a validation middleware for search
     */
    search: () => (0, exports.validateQuery)(exports.commonSchemas.search),
    /**
     * Create a validation middleware for language preference
     */
    language: () => (0, exports.validateQuery)(exports.commonSchemas.language),
    /**
     * Create a validation middleware for date range
     */
    dateRange: () => (0, exports.validateQuery)(exports.commonSchemas.dateRange),
    /**
     * Create a validation middleware for price range
     */
    priceRange: () => (0, exports.validateQuery)(exports.commonSchemas.priceRange),
    /**
     * Validate Arabic text content
     */
    arabicText: (text) => {
        const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
        return arabicRegex.test(text);
    },
    /**
     * Validate Syrian phone number
     */
    syrianPhone: (phone) => {
        const syrianPhoneRegex = /^(\+963|0)?[0-9]{8,9}$/;
        return syrianPhoneRegex.test(phone);
    },
    /**
     * Validate file type
     */
    fileType: (filename, allowedTypes) => {
        const extension = filename.split('.').pop()?.toLowerCase();
        return extension ? allowedTypes.includes(extension) : false;
    },
    /**
     * Sanitize HTML content
     */
    sanitizeHtml: (html) => {
        // Basic HTML sanitization - in production, use a proper library like DOMPurify
        return html
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
            .replace(/javascript:/gi, '')
            .replace(/on\w+\s*=/gi, '');
    },
    /**
     * Validate and normalize email
     */
    normalizeEmail: (email) => {
        return email.toLowerCase().trim();
    },
    /**
     * Validate and normalize phone number
     */
    normalizePhone: (phone) => {
        // Remove all non-digit characters except +
        let normalized = phone.replace(/[^\d+]/g, '');
        // Add +963 prefix if not present
        if (!normalized.startsWith('+963') && !normalized.startsWith('963')) {
            if (normalized.startsWith('0')) {
                normalized = '+963' + normalized.substring(1);
            }
            else {
                normalized = '+963' + normalized;
            }
        }
        else if (normalized.startsWith('963')) {
            normalized = '+' + normalized;
        }
        return normalized;
    },
};
exports.default = exports.validate;
//# sourceMappingURL=validation.js.map