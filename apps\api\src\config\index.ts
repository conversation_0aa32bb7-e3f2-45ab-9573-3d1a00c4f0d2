import dotenv from 'dotenv';
import { z } from 'zod';
import path from 'path';

// Load environment variables
// Try multiple possible locations for .env file
const possibleEnvPaths = [
  path.resolve(process.cwd(), '.env'),
  path.resolve(__dirname, '../../.env'),
  path.resolve(__dirname, '../../../.env'),
];

let envLoaded = false;
for (const envPath of possibleEnvPaths) {
  if (require('fs').existsSync(envPath)) {
    console.log('Loading .env from:', envPath);
    const result = dotenv.config({ path: envPath });
    if (!result.error) {
      console.log('✅ .env loaded successfully');
      envLoaded = true;
      break;
    }
  }
}

if (!envLoaded) {
  console.log('⚠️ No .env file found, using environment variables');
  dotenv.config(); // Fallback to default behavior
}

// Environment validation schema
const envSchema = z.object({
  // Database
  DATABASE_URL: z.string().url('Invalid database URL'),
  
  // Redis
  REDIS_URL: z.string().url('Invalid Redis URL'),
  
  // JWT
  JWT_SECRET: z.string().min(32, 'JWT secret must be at least 32 characters'),
  JWT_REFRESH_SECRET: z.string().min(32, 'JWT refresh secret must be at least 32 characters'),
  JWT_EXPIRES_IN: z.string().default('15m'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('7d'),
  
  // Server
  PORT: z.string().transform(Number).pipe(z.number().min(1000).max(65535)).default('3001'),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  API_VERSION: z.string().default('v1'),
  CORS_ORIGIN: z.string().default('http://localhost:3000'),
  
  // Email
  SMTP_HOST: z.string().min(1, 'SMTP host is required'),
  SMTP_PORT: z.string().transform(Number).pipe(z.number().min(1).max(65535)).default('587'),
  SMTP_USER: z.string().email('Invalid SMTP user email'),
  SMTP_PASS: z.string().min(1, 'SMTP password is required'),
  FROM_EMAIL: z.string().email('Invalid from email'),
  FROM_NAME: z.string().default('Freela Syria'),
  
  // File Upload
  UPLOAD_MAX_SIZE: z.string().transform(Number).pipe(z.number().min(1)).default('10485760'),
  UPLOAD_ALLOWED_TYPES: z.string().default('jpg,jpeg,png,gif,webp,pdf,doc,docx,txt'),
  UPLOAD_PATH: z.string().default('uploads'),
  CDN_URL: z.string().url().optional(),
  
  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).pipe(z.number().min(1)).default('900000'),
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).pipe(z.number().min(1)).default('100'),
  
  // Security
  BCRYPT_ROUNDS: z.string().transform(Number).pipe(z.number().min(10).max(15)).default('12'),
  SESSION_SECRET: z.string().min(32, 'Session secret must be at least 32 characters'),
  
  // External APIs
  OPENAI_API_KEY: z.string().optional(),
  OPENROUTER_API_KEY: z.string().optional(),
  GOOGLE_MAPS_API_KEY: z.string().optional(),

  // Supabase
  SUPABASE_URL: z.string().url('Invalid Supabase URL').optional(),
  SUPABASE_ANON_KEY: z.string().optional(),
  SUPABASE_SERVICE_ROLE_KEY: z.string().optional(),
  
  // Monitoring
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  SENTRY_DSN: z.string().optional(),
  
  // Payment
  STRIPE_SECRET_KEY: z.string().optional(),
  STRIPE_WEBHOOK_SECRET: z.string().optional(),
  PAYPAL_CLIENT_ID: z.string().optional(),
  PAYPAL_CLIENT_SECRET: z.string().optional(),
});

// Validate environment variables
const envValidation = envSchema.safeParse(process.env);

if (!envValidation.success) {
  console.error('❌ Invalid environment variables:');
  console.error(envValidation.error.format());
  process.exit(1);
}

export const config = envValidation.data;

// Derived configurations
export const isDevelopment = config.NODE_ENV === 'development';
export const isProduction = config.NODE_ENV === 'production';
export const isTest = config.NODE_ENV === 'test';

// CORS origins array
export const corsOrigins = config.CORS_ORIGIN.split(',').map(origin => origin.trim());

// Upload allowed types array
export const uploadAllowedTypes = config.UPLOAD_ALLOWED_TYPES.split(',').map(type => type.trim());

// Database configuration
export const databaseConfig = {
  url: config.DATABASE_URL,
};

// Redis configuration
export const redisConfig = {
  url: config.REDIS_URL,
};

// JWT configuration
export const jwtConfig = {
  secret: config.JWT_SECRET,
  refreshSecret: config.JWT_REFRESH_SECRET,
  expiresIn: config.JWT_EXPIRES_IN,
  refreshExpiresIn: config.JWT_REFRESH_EXPIRES_IN,
};

// Email configuration
export const emailConfig = {
  host: config.SMTP_HOST,
  port: config.SMTP_PORT,
  user: config.SMTP_USER,
  pass: config.SMTP_PASS,
  from: {
    email: config.FROM_EMAIL,
    name: config.FROM_NAME,
  },
};

// File upload configuration
export const uploadConfig = {
  maxSize: config.UPLOAD_MAX_SIZE,
  allowedTypes: uploadAllowedTypes,
  path: config.UPLOAD_PATH,
  cdnUrl: config.CDN_URL,
};

// Rate limiting configuration
export const rateLimitConfig = {
  windowMs: config.RATE_LIMIT_WINDOW_MS,
  maxRequests: config.RATE_LIMIT_MAX_REQUESTS,
};

// Security configuration
export const securityConfig = {
  bcryptRounds: config.BCRYPT_ROUNDS,
  sessionSecret: config.SESSION_SECRET,
};

// External APIs configuration
export const externalApisConfig = {
  openai: {
    apiKey: config.OPENAI_API_KEY,
  },
  openrouter: {
    apiKey: config.OPENROUTER_API_KEY,
  },
  googleMaps: {
    apiKey: config.GOOGLE_MAPS_API_KEY,
  },
};

// Supabase configuration
export const supabaseConfig = {
  url: config.SUPABASE_URL,
  anonKey: config.SUPABASE_ANON_KEY,
  serviceRoleKey: config.SUPABASE_SERVICE_ROLE_KEY,
};

// Monitoring configuration
export const monitoringConfig = {
  logLevel: config.LOG_LEVEL,
  sentryDsn: config.SENTRY_DSN,
};

// Payment configuration
export const paymentConfig = {
  stripe: {
    secretKey: config.STRIPE_SECRET_KEY,
    webhookSecret: config.STRIPE_WEBHOOK_SECRET,
  },
  paypal: {
    clientId: config.PAYPAL_CLIENT_ID,
    clientSecret: config.PAYPAL_CLIENT_SECRET,
  },
};

export default config;
