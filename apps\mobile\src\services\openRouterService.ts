/**
 * OpenRouter API Service for Mobile App
 * Handles AI requests with Arabic language support
 */

interface OpenRouterOptions {
  model?: string;
  temperature?: number;
  max_tokens?: number;
}

interface OpenRouterResponse {
  content: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

class OpenRouterService {
  private apiKey: string;
  private baseUrl: string = 'https://openrouter.ai/api/v1';

  constructor() {
    // Use the API key from environment or fallback
    this.apiKey = process.env.OPENROUTER_API_KEY || '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';
  }

  /**
   * Generate AI response
   */
  async generateResponse(
    prompt: string,
    options: OpenRouterOptions = {}
  ): Promise<string> {
    try {
      const {
        model = 'openai/gpt-4-turbo-preview',
        temperature = 0.7,
        max_tokens = 1000,
      } = options;

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://freela-syria.com',
          'X-Title': 'Freela Syria Mobile App',
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: 'user',
              content: prompt,
            },
          ],
          temperature,
          max_tokens,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenRouter API error: ${response.status}`);
      }

      const data = await response.json();
      return data.choices[0]?.message?.content || '';

    } catch (error) {
      console.error('OpenRouter API error:', error);
      throw error;
    }
  }

  /**
   * Chat completion with conversation history
   */
  async chatCompletion(
    messages: Array<{ role: string; content: string }>,
    context?: string,
    options: OpenRouterOptions = {}
  ): Promise<OpenRouterResponse> {
    try {
      const {
        model = 'openai/gpt-4-turbo-preview',
        temperature = 0.7,
        max_tokens = 1000,
      } = options;

      // Add context as system message if provided
      const systemMessages = context
        ? [{ role: 'system', content: context }]
        : [];

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://freela-syria.com',
          'X-Title': 'Freela Syria Mobile App',
        },
        body: JSON.stringify({
          model,
          messages: [...systemMessages, ...messages],
          temperature,
          max_tokens,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenRouter API error: ${response.status}`);
      }

      const data = await response.json();
      
      return {
        content: data.choices[0]?.message?.content || '',
        usage: data.usage,
      };

    } catch (error) {
      console.error('OpenRouter chat completion error:', error);
      throw error;
    }
  }

  /**
   * Analyze image with vision model
   */
  async analyzeImage(
    imageBase64: string,
    prompt: string,
    options: OpenRouterOptions = {}
  ): Promise<string> {
    try {
      const {
        model = 'openai/gpt-4-vision-preview',
        temperature = 0.4,
        max_tokens = 1500,
      } = options;

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://freela-syria.com',
          'X-Title': 'Freela Syria Mobile App',
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: prompt,
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: `data:image/jpeg;base64,${imageBase64}`,
                  },
                },
              ],
            },
          ],
          temperature,
          max_tokens,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenRouter Vision API error: ${response.status}`);
      }

      const data = await response.json();
      return data.choices[0]?.message?.content || '';

    } catch (error) {
      console.error('OpenRouter image analysis error:', error);
      throw error;
    }
  }

  /**
   * Transcribe audio (placeholder - would need Whisper API)
   */
  async transcribeAudio(audioBase64: string): Promise<string> {
    // For now, return a placeholder
    // In a real implementation, this would use Whisper API
    return 'تحويل الصوت إلى نص غير متاح حالياً';
  }
}

export const openRouterService = new OpenRouterService();
