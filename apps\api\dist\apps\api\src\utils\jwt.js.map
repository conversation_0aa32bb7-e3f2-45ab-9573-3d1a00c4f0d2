{"version": 3, "file": "jwt.js", "sourceRoot": "", "sources": ["../../../../../src/utils/jwt.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;AAEH,gEAAgD;AAChD,iCAAqC;AACrC,qCAAiD;AA2BjD,qCAAqC;AACrC,MAAM,UAAU,GAAG;IACjB,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,iCAAiC;IACjF,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,kCAAkC;IACpF,iBAAiB,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,KAAK,CAAW;IACzE,kBAAkB,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,IAAI,CAAW;IAC1E,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,kBAAkB;IACpD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,kBAAkB;CACzD,CAAC;AAEF,iDAAiD;AACjD,IAAI,WAAW,GAA2C,IAAI,CAAC;AAE/D,uCAAuC;AACvC,MAAM,eAAe,GAAG,KAAK,IAAI,EAAE;IACjC,IAAI,CAAC;QACH,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;YAC1B,WAAW,GAAG,IAAA,oBAAY,EAAC;gBACzB,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS;aAC3B,CAAC,CAAC;YACH,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;YAC5B,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,IAAI,CAAC,2CAA2C,EAAG,KAAe,CAAC,OAAO,CAAC,CAAC;QACpF,WAAW,GAAG,IAAI,CAAC;IACrB,CAAC;AACH,CAAC,CAAC;AAEF,kCAAkC;AAClC,eAAe,EAAE,CAAC;AAElB;;GAEG;AACU,QAAA,QAAQ,GAAG;IACtB;;OAEG;IACH,mBAAmB,EAAE,CAAC,OAAwC,EAAU,EAAE;QACxE,IAAI,CAAC;YACH,OAAO,sBAAG,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,aAAa,EAAE;gBACjD,SAAS,EAAE,UAAU,CAAC,iBAAiB;gBACvC,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,QAAQ,EAAE,UAAU,CAAC,QAAQ;aACf,CAAC,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,oBAAW,CAAC,aAAa,CAC7B,EAAE,SAAS,EAAE,qBAAqB,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,EACrE,yBAAyB,CAC1B,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,oBAAoB,EAAE,CAAC,OAAwC,EAAU,EAAE;QACzE,IAAI,CAAC;YACH,OAAO,sBAAG,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,cAAc,EAAE;gBAClD,SAAS,EAAE,UAAU,CAAC,kBAAkB;gBACxC,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,QAAQ,EAAE,UAAU,CAAC,QAAQ;aACf,CAAC,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,oBAAW,CAAC,aAAa,CAC7B,EAAE,SAAS,EAAE,sBAAsB,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,EACtE,0BAA0B,CAC3B,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,iBAAiB,EAAE,CAAC,OAAwC,EAAa,EAAE;QACzE,MAAM,WAAW,GAAG,gBAAQ,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAC1D,MAAM,YAAY,GAAG,gBAAQ,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAE5D,wCAAwC;QACxC,MAAM,eAAe,GAAG,gBAAQ,CAAC,sBAAsB,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QACtF,MAAM,gBAAgB,GAAG,gBAAQ,CAAC,sBAAsB,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC;QAExF,OAAO;YACL,WAAW;YACX,YAAY;YACZ,SAAS,EAAE,eAAe;YAC1B,gBAAgB,EAAE,gBAAgB;SACnC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,iBAAiB,EAAE,KAAK,EAAE,KAAa,EAAuB,EAAE;QAC9D,IAAI,CAAC;YACH,gCAAgC;YAChC,IAAI,MAAM,gBAAQ,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC7C,MAAM,oBAAW,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;YACvD,CAAC;YAED,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,aAAa,EAAE;gBAC1D,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,QAAQ,EAAE,UAAU,CAAC,QAAQ;aAC9B,CAAe,CAAC;YAEjB,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,iBAAQ,EAAE,CAAC;gBAC9B,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,GAAG,GAAG,KAAY,CAAC;YACzB,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;gBACrC,MAAM,oBAAW,CAAC,YAAY,EAAE,CAAC;YACnC,CAAC;YAED,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;gBACrC,MAAM,oBAAW,CAAC,YAAY,EAAE,CAAC;YACnC,CAAC;YAED,MAAM,oBAAW,CAAC,aAAa,CAC7B,EAAE,SAAS,EAAE,mBAAmB,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,EACnE,6BAA6B,CAC9B,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB,EAAE,KAAK,EAAE,KAAa,EAAuB,EAAE;QAC/D,IAAI,CAAC;YACH,gCAAgC;YAChC,IAAI,MAAM,gBAAQ,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC7C,MAAM,oBAAW,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;YACtD,CAAC;YAED,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,cAAc,EAAE;gBAC3D,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,QAAQ,EAAE,UAAU,CAAC,QAAQ;aAC9B,CAAe,CAAC;YAEjB,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,iBAAQ,EAAE,CAAC;gBAC9B,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,GAAG,GAAG,KAAY,CAAC;YACzB,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;gBACrC,MAAM,oBAAW,CAAC,YAAY,EAAE,CAAC;YACnC,CAAC;YAED,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;gBACrC,MAAM,oBAAW,CAAC,YAAY,EAAE,CAAC;YACnC,CAAC;YAED,MAAM,oBAAW,CAAC,aAAa,CAC7B,EAAE,SAAS,EAAE,oBAAoB,EAAE,KAAK,EAAG,KAAe,CAAC,OAAO,EAAE,EACpE,8BAA8B,CAC/B,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB,EAAE,KAAK,EAAE,YAAoB,EAAsB,EAAE;QACrE,MAAM,OAAO,GAAG,MAAM,gBAAQ,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;QAEhE,0BAA0B;QAC1B,MAAM,YAAY,GAAG,gBAAQ,CAAC,iBAAiB,CAAC;YAC9C,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,SAAS,EAAE,OAAO,CAAC,SAAS;SAC7B,CAAC,CAAC;QAEH,kCAAkC;QAClC,MAAM,gBAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAE5C,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,cAAc,EAAE,KAAK,EAAE,KAAa,EAAiB,EAAE;QACrD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC3D,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,sCAAsC;YACtC,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,CAAQ,CAAC;YACzC,IAAI,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;gBAC3B,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;gBACxD,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;oBACZ,MAAM,WAAW,CAAC,KAAK,CAAC,aAAa,KAAK,EAAE,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;gBAC7D,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAG,KAAe,CAAC,OAAO,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB,EAAE,KAAK,EAAE,KAAa,EAAoB,EAAE;QAC5D,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,KAAK,CAAC,CAAC,6DAA6D;QAC7E,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,GAAG,CAAC,aAAa,KAAK,EAAE,CAAC,CAAC;YAC3D,OAAO,MAAM,KAAK,MAAM,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAG,KAAe,CAAC,OAAO,CAAC,CAAC;YAC3E,OAAO,KAAK,CAAC,CAAC,8CAA8C;QAC9D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,sBAAsB,EAAE,CAAC,SAA0B,EAAU,EAAE;QAC7D,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;YAClC,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,6CAA6C;QAC7C,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACjD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,6BAA6B,SAAS,EAAE,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEtB,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,CAAC;YACvB,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,GAAG,EAAE,CAAC;YAC5B,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;YACjC,KAAK,GAAG,CAAC,CAAC,OAAO,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;YACtC,OAAO,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,sBAAsB,IAAI,EAAE,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,sBAAsB,EAAE,CAAC,UAAmB,EAAiB,EAAE;QAC7D,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;YAChD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;CACF,CAAC;AAEF,6CAA6C;AAC7C,kBAAe,gBAAQ,CAAC"}