{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../../../../src/utils/errors.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAIH,2BAA2B;AACd,QAAA,WAAW,GAAG;IACzB,EAAE,EAAE,GAAG;IACP,OAAO,EAAE,GAAG;IACZ,UAAU,EAAE,GAAG;IACf,WAAW,EAAE,GAAG;IAChB,YAAY,EAAE,GAAG;IACjB,SAAS,EAAE,GAAG;IACd,SAAS,EAAE,GAAG;IACd,QAAQ,EAAE,GAAG;IACb,oBAAoB,EAAE,GAAG;IACzB,iBAAiB,EAAE,GAAG;IACtB,qBAAqB,EAAE,GAAG;IAC1B,WAAW,EAAE,GAAG;IAChB,mBAAmB,EAAE,GAAG;CAChB,CAAC;AAEX,iCAAiC;AACjC,IAAY,SAaX;AAbD,WAAY,SAAS;IACnB,sCAAyB,CAAA;IACzB,8CAAiC,CAAA;IACjC,4CAA+B,CAAA;IAC/B,oCAAuB,CAAA;IACvB,kCAAqB,CAAA;IACrB,sCAAyB,CAAA;IACzB,kDAAqC,CAAA;IACrC,kCAAqB,CAAA;IACrB,wCAA2B,CAAA;IAC3B,sCAAyB,CAAA;IACzB,oCAAuB,CAAA;IACvB,kCAAqB,CAAA;AACvB,CAAC,EAbW,SAAS,yBAAT,SAAS,QAapB;AAED,wBAAwB;AACxB,IAAY,aAKX;AALD,WAAY,aAAa;IACvB,4BAAW,CAAA;IACX,kCAAiB,CAAA;IACjB,8BAAa,CAAA;IACb,sCAAqB,CAAA;AACvB,CAAC,EALW,aAAa,6BAAb,aAAa,QAKxB;AAED,4CAA4C;AAC/B,QAAA,aAAa,GAAG;IAC3B,wBAAwB;IACxB,mBAAmB,EAAE;QACnB,EAAE,EAAE,2BAA2B;QAC/B,EAAE,EAAE,4CAA4C;KACjD;IACD,aAAa,EAAE;QACb,EAAE,EAAE,kCAAkC;QACtC,EAAE,EAAE,2BAA2B;KAChC;IACD,aAAa,EAAE;QACb,EAAE,EAAE,8BAA8B;QAClC,EAAE,EAAE,uBAAuB;KAC5B;IACD,aAAa,EAAE;QACb,EAAE,EAAE,yCAAyC;QAC7C,EAAE,EAAE,kCAAkC;KACvC;IAED,oBAAoB;IACpB,iBAAiB,EAAE;QACjB,EAAE,EAAE,mBAAmB;QACvB,EAAE,EAAE,+BAA+B;KACpC;IACD,cAAc,EAAE;QACd,EAAE,EAAE,wBAAwB;QAC5B,EAAE,EAAE,iBAAiB;KACtB;IACD,aAAa,EAAE;QACb,EAAE,EAAE,sBAAsB;QAC1B,EAAE,EAAE,kCAAkC;KACvC;IACD,aAAa,EAAE;QACb,EAAE,EAAE,6BAA6B;QACjC,EAAE,EAAE,2BAA2B;KAChC;IAED,kBAAkB;IAClB,cAAc,EAAE;QACd,EAAE,EAAE,gBAAgB;QACpB,EAAE,EAAE,oBAAoB;KACzB;IACD,gBAAgB,EAAE;QAChB,EAAE,EAAE,kBAAkB;QACtB,EAAE,EAAE,kBAAkB;KACvB;IACD,iBAAiB,EAAE;QACjB,EAAE,EAAE,mBAAmB;QACvB,EAAE,EAAE,mBAAmB;KACxB;IACD,iBAAiB,EAAE;QACjB,EAAE,EAAE,mBAAmB;QACvB,EAAE,EAAE,iBAAiB;KACtB;IAED,kBAAkB;IAClB,YAAY,EAAE;QACZ,EAAE,EAAE,8BAA8B;QAClC,EAAE,EAAE,sCAAsC;KAC3C;IACD,YAAY,EAAE;QACZ,EAAE,EAAE,6BAA6B;QACjC,EAAE,EAAE,yBAAyB;KAC9B;IAED,oBAAoB;IACpB,sBAAsB,EAAE;QACtB,EAAE,EAAE,uCAAuC;QAC3C,EAAE,EAAE,wCAAwC;KAC7C;IACD,iBAAiB,EAAE;QACjB,EAAE,EAAE,2BAA2B;QAC/B,EAAE,EAAE,oCAAoC;KACzC;IACD,kBAAkB,EAAE;QAClB,EAAE,EAAE,4BAA4B;QAChC,EAAE,EAAE,oCAAoC;KACzC;IAED,qBAAqB;IACrB,cAAc,EAAE;QACd,EAAE,EAAE,iCAAiC;QACrC,EAAE,EAAE,8BAA8B;KACnC;IACD,iBAAiB,EAAE;QACjB,EAAE,EAAE,mBAAmB;QACvB,EAAE,EAAE,oBAAoB;KACzB;IAED,gBAAgB;IAChB,mBAAmB,EAAE;QACnB,EAAE,EAAE,2CAA2C;QAC/C,EAAE,EAAE,iDAAiD;KACtD;IAED,iBAAiB;IACjB,cAAc,EAAE;QACd,EAAE,EAAE,uBAAuB;QAC3B,EAAE,EAAE,qBAAqB;KAC1B;IACD,mBAAmB,EAAE;QACnB,EAAE,EAAE,iCAAiC;QACrC,EAAE,EAAE,yBAAyB;KAC9B;IACD,cAAc,EAAE;QACd,EAAE,EAAE,2BAA2B;QAC/B,EAAE,EAAE,2BAA2B;KAChC;CACO,CAAC;AAEX,iCAAiC;AACjC,MAAa,QAAS,SAAQ,KAAK;IACjB,UAAU,CAAS;IACnB,IAAI,CAAY;IAChB,QAAQ,CAAgB;IACxB,aAAa,CAAU;IACvB,SAAS,CAAS;IAClB,SAAS,CAAU;IAC5B,OAAO,CAAuB;IAC9B,IAAI,CAAU;IACd,MAAM,CAAU;IAChB,MAAM,CAAU;IAChB,SAAS,CAAU;IAE1B,YACE,OAAe,EACf,aAAqB,mBAAW,CAAC,qBAAqB,EACtD,OAAkB,SAAS,CAAC,QAAQ,EACpC,WAA0B,aAAa,CAAC,MAAM,EAC9C,gBAAyB,IAAI,EAC7B,SAAkB,EAClB,OAA6B;QAE7B,KAAK,CAAC,OAAO,CAAC,CAAC;QAEf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAClC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC1C,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,sBAAsB;QACtB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;IAED,+BAA+B;IAC/B,UAAU,CAAC,GAAY;QACrB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,cAAc,CAAW,CAAC;QACvD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,oCAAoC;IACpC,MAAM;QACJ,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC9C,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;gBACrC,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC3C,GAAG,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC;aACrD;SACF,CAAC;IACJ,CAAC;CACF;AA/DD,4BA+DC;AAED,2BAA2B;AACd,QAAA,WAAW,GAAG;IACzB,wBAAwB;IACxB,kBAAkB,EAAE,CAAC,SAAkB,EAAE,EAAE,CACzC,IAAI,QAAQ,CACV,qBAAa,CAAC,mBAAmB,CAAC,EAAE,EACpC,mBAAW,CAAC,YAAY,EACxB,SAAS,CAAC,cAAc,EACxB,aAAa,CAAC,MAAM,EACpB,IAAI,EACJ,SAAS,IAAI,qBAAa,CAAC,mBAAmB,CAAC,EAAE,CAClD;IAEH,YAAY,EAAE,CAAC,SAAkB,EAAE,EAAE,CACnC,IAAI,QAAQ,CACV,qBAAa,CAAC,aAAa,CAAC,EAAE,EAC9B,mBAAW,CAAC,YAAY,EACxB,SAAS,CAAC,cAAc,EACxB,aAAa,CAAC,MAAM,EACpB,IAAI,EACJ,SAAS,IAAI,qBAAa,CAAC,aAAa,CAAC,EAAE,CAC5C;IAEH,YAAY,EAAE,CAAC,SAAkB,EAAE,EAAE,CACnC,IAAI,QAAQ,CACV,qBAAa,CAAC,aAAa,CAAC,EAAE,EAC9B,mBAAW,CAAC,YAAY,EACxB,SAAS,CAAC,cAAc,EACxB,aAAa,CAAC,MAAM,EACpB,IAAI,EACJ,SAAS,IAAI,qBAAa,CAAC,aAAa,CAAC,EAAE,CAC5C;IAEH,YAAY,EAAE,CAAC,SAAkB,EAAE,EAAE,CACnC,IAAI,QAAQ,CACV,qBAAa,CAAC,aAAa,CAAC,EAAE,EAC9B,mBAAW,CAAC,SAAS,EACrB,SAAS,CAAC,aAAa,EACvB,aAAa,CAAC,IAAI,EAClB,IAAI,EACJ,SAAS,IAAI,qBAAa,CAAC,aAAa,CAAC,EAAE,CAC5C;IAEH,oBAAoB;IACpB,gBAAgB,EAAE,CAAC,OAA6B,EAAE,SAAkB,EAAE,EAAE,CACtE,IAAI,QAAQ,CACV,qBAAa,CAAC,iBAAiB,CAAC,EAAE,EAClC,mBAAW,CAAC,WAAW,EACvB,SAAS,CAAC,UAAU,EACpB,aAAa,CAAC,GAAG,EACjB,IAAI,EACJ,SAAS,IAAI,qBAAa,CAAC,iBAAiB,CAAC,EAAE,EAC/C,OAAO,CACR;IAEH,4BAA4B;IAC5B,YAAY,EAAE,CAAC,MAAe,EAAE,SAAkB,EAAE,EAAE,CACpD,IAAI,QAAQ,CACV,qBAAa,CAAC,cAAc,CAAC,EAAE,EAC/B,mBAAW,CAAC,SAAS,EACrB,SAAS,CAAC,SAAS,EACnB,aAAa,CAAC,GAAG,EACjB,IAAI,EACJ,SAAS,IAAI,qBAAa,CAAC,cAAc,CAAC,EAAE,EAC5C,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,SAAS,CAChC;IAEH,cAAc,EAAE,CAAC,QAAiB,EAAE,SAAkB,EAAE,EAAE,CACxD,IAAI,QAAQ,CACV,qBAAa,CAAC,gBAAgB,CAAC,EAAE,EACjC,mBAAW,CAAC,SAAS,EACrB,SAAS,CAAC,SAAS,EACnB,aAAa,CAAC,GAAG,EACjB,IAAI,EACJ,SAAS,IAAI,qBAAa,CAAC,gBAAgB,CAAC,EAAE,EAC9C,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS,CACpC;IAEH,eAAe,EAAE,CAAC,SAAkB,EAAE,SAAkB,EAAE,EAAE,CAC1D,IAAI,QAAQ,CACV,qBAAa,CAAC,iBAAiB,CAAC,EAAE,EAClC,mBAAW,CAAC,SAAS,EACrB,SAAS,CAAC,SAAS,EACnB,aAAa,CAAC,GAAG,EACjB,IAAI,EACJ,SAAS,IAAI,qBAAa,CAAC,iBAAiB,CAAC,EAAE,EAC/C,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,SAAS,CACtC;IAEH,kBAAkB;IAClB,WAAW,EAAE,CAAC,KAAc,EAAE,SAAkB,EAAE,EAAE,CAClD,IAAI,QAAQ,CACV,qBAAa,CAAC,YAAY,CAAC,EAAE,EAC7B,mBAAW,CAAC,QAAQ,EACpB,SAAS,CAAC,QAAQ,EAClB,aAAa,CAAC,GAAG,EACjB,IAAI,EACJ,SAAS,IAAI,qBAAa,CAAC,YAAY,CAAC,EAAE,EAC1C,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAC9B;IAEH,oBAAoB;IACpB,oBAAoB,EAAE,CAAC,OAA6B,EAAE,SAAkB,EAAE,EAAE,CAC1E,IAAI,QAAQ,CACV,qBAAa,CAAC,sBAAsB,CAAC,EAAE,EACvC,mBAAW,CAAC,mBAAmB,EAC/B,SAAS,CAAC,UAAU,EACpB,aAAa,CAAC,IAAI,EAClB,IAAI,EACJ,SAAS,IAAI,qBAAa,CAAC,sBAAsB,CAAC,EAAE,EACpD,OAAO,CACR;IAEH,eAAe,EAAE,CAAC,OAA6B,EAAE,SAAkB,EAAE,EAAE,CACrE,IAAI,QAAQ,CACV,qBAAa,CAAC,iBAAiB,CAAC,EAAE,EAClC,mBAAW,CAAC,iBAAiB,EAC7B,SAAS,CAAC,UAAU,EACpB,aAAa,CAAC,MAAM,EACpB,IAAI,EACJ,SAAS,IAAI,qBAAa,CAAC,iBAAiB,CAAC,EAAE,EAC/C,OAAO,CACR;IAEH,gBAAgB,EAAE,CAAC,OAA6B,EAAE,SAAkB,EAAE,EAAE,CACtE,IAAI,QAAQ,CACV,qBAAa,CAAC,kBAAkB,CAAC,EAAE,EACnC,mBAAW,CAAC,WAAW,EACvB,SAAS,CAAC,UAAU,EACpB,aAAa,CAAC,GAAG,EACjB,IAAI,EACJ,SAAS,IAAI,qBAAa,CAAC,kBAAkB,CAAC,EAAE,EAChD,OAAO,CACR;IAEH,gBAAgB;IAChB,iBAAiB,EAAE,CAAC,OAA6B,EAAE,SAAkB,EAAE,EAAE,CACvE,IAAI,QAAQ,CACV,qBAAa,CAAC,mBAAmB,CAAC,EAAE,EACpC,mBAAW,CAAC,iBAAiB,EAC7B,SAAS,CAAC,UAAU,EACpB,aAAa,CAAC,MAAM,EACpB,IAAI,EACJ,SAAS,IAAI,qBAAa,CAAC,mBAAmB,CAAC,EAAE,EACjD,OAAO,CACR;IAEH,iBAAiB;IACjB,aAAa,EAAE,CAAC,OAA6B,EAAE,SAAkB,EAAE,EAAE,CACnE,IAAI,QAAQ,CACV,qBAAa,CAAC,cAAc,CAAC,EAAE,EAC/B,mBAAW,CAAC,qBAAqB,EACjC,SAAS,CAAC,QAAQ,EAClB,aAAa,CAAC,QAAQ,EACtB,KAAK,EACL,SAAS,IAAI,qBAAa,CAAC,cAAc,CAAC,EAAE,EAC5C,OAAO,CACR;IAEH,aAAa,EAAE,CAAC,OAA6B,EAAE,SAAkB,EAAE,EAAE,CACnE,IAAI,QAAQ,CACV,qBAAa,CAAC,cAAc,CAAC,EAAE,EAC/B,mBAAW,CAAC,qBAAqB,EACjC,SAAS,CAAC,QAAQ,EAClB,aAAa,CAAC,IAAI,EAClB,IAAI,EACJ,SAAS,IAAI,qBAAa,CAAC,cAAc,CAAC,EAAE,EAC5C,OAAO,CACR;IAEH,gDAAgD;IAChD,QAAQ,EAAE,CAAC,OAAgB,EAAE,SAAkB,EAAE,OAA6B,EAAE,EAAE,CAChF,IAAI,QAAQ,CACV,OAAO,IAAI,oBAAoB,EAC/B,mBAAW,CAAC,SAAS,EACrB,SAAS,CAAC,SAAS,EACnB,aAAa,CAAC,GAAG,EACjB,IAAI,EACJ,SAAS,IAAI,kBAAkB,EAC/B,OAAO,CACR;IAEH,SAAS,EAAE,CAAC,OAAgB,EAAE,SAAkB,EAAE,OAA6B,EAAE,EAAE,CACjF,IAAI,QAAQ,CACV,OAAO,IAAI,kBAAkB,EAC7B,mBAAW,CAAC,SAAS,EACrB,SAAS,CAAC,aAAa,EACvB,aAAa,CAAC,MAAM,EACpB,IAAI,EACJ,SAAS,IAAI,cAAc,EAC3B,OAAO,CACR;IAEH,UAAU,EAAE,CAAC,OAAgB,EAAE,SAAkB,EAAE,OAA6B,EAAE,EAAE,CAClF,IAAI,QAAQ,CACV,OAAO,IAAI,aAAa,EACxB,mBAAW,CAAC,WAAW,EACvB,SAAS,CAAC,UAAU,EACpB,aAAa,CAAC,GAAG,EACjB,IAAI,EACJ,SAAS,IAAI,cAAc,EAC3B,OAAO,CACR;IAEH,YAAY,EAAE,CAAC,OAAgB,EAAE,SAAkB,EAAE,OAA6B,EAAE,EAAE,CACpF,IAAI,QAAQ,CACV,OAAO,IAAI,qBAAqB,EAChC,mBAAW,CAAC,YAAY,EACxB,SAAS,CAAC,cAAc,EACxB,aAAa,CAAC,MAAM,EACpB,IAAI,EACJ,SAAS,IAAI,kBAAkB,EAC/B,OAAO,CACR;IAEH,eAAe,EAAE,CAAC,OAAgB,EAAE,SAAkB,EAAE,OAA6B,EAAE,EAAE,CACvF,IAAI,QAAQ,CACV,OAAO,IAAI,mBAAmB,EAC9B,mBAAW,CAAC,iBAAiB,EAC7B,SAAS,CAAC,UAAU,EACpB,aAAa,CAAC,MAAM,EACpB,IAAI,EACJ,SAAS,IAAI,kBAAkB,EAC/B,OAAO,CACR;IAEH,mCAAmC;IACnC,mBAAmB,EAAE,CAAC,OAAgB,EAAE,SAAkB,EAAE,OAA6B,EAAE,EAAE,CAC3F,IAAI,QAAQ,CACV,OAAO,IAAI,qBAAa,CAAC,cAAc,CAAC,EAAE,EAC1C,mBAAW,CAAC,qBAAqB,EACjC,SAAS,CAAC,QAAQ,EAClB,aAAa,CAAC,QAAQ,EACtB,KAAK,EACL,SAAS,IAAI,qBAAa,CAAC,cAAc,CAAC,EAAE,EAC5C,OAAO,CACR;IAEH,uBAAuB;IACvB,MAAM,EAAE,CACN,OAAe,EACf,UAAkB,EAClB,IAAe,EACf,WAA0B,aAAa,CAAC,MAAM,EAC9C,SAAkB,EAClB,OAA6B,EAC7B,EAAE,CACF,IAAI,QAAQ,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC;CAC9E,CAAC;AAEF,oDAAoD;AAC7C,MAAM,kBAAkB,GAAG,CAAC,KAAY,EAAW,EAAE;IAC1D,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;QAC9B,OAAO,KAAK,CAAC,aAAa,CAAC;IAC7B,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AALW,QAAA,kBAAkB,sBAK7B"}