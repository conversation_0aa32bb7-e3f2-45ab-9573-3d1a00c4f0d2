{"version": 3, "file": "openrouter.js", "sourceRoot": "", "sources": ["../../../../../src/services/openrouter.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;AAEH,kDAA4D;AAC5D,4CAAyC;AACzC,4CAA8C;AAE9C,+BAA+B;AAC/B,MAAM,kBAAkB,GAAG,8BAA8B,CAAC;AAC1D,MAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,2EAA2E,CAAC;AAEzI,8CAA8C;AACjC,QAAA,SAAS,GAAG;IACvB,WAAW,EAAE,4BAA4B;IACzC,eAAe,EAAE,oCAAoC;IACrD,UAAU,EAAE,mBAAmB;IAC/B,aAAa,EAAE,sBAAsB;CAC7B,CAAC;AA+BX,MAAM,iBAAiB;IACb,MAAM,CAAgB;IACtB,YAAY,GAAY,iBAAS,CAAC,WAAW,CAAC;IAEtD;QACE,IAAI,CAAC,MAAM,GAAG,eAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,kBAAkB;YAC3B,OAAO,EAAE;gBACP,eAAe,EAAE,UAAU,kBAAkB,EAAE;gBAC/C,cAAc,EAAE,kBAAkB;gBAClC,cAAc,EAAE,0BAA0B;gBAC1C,SAAS,EAAE,4BAA4B;aACxC;YACD,OAAO,EAAE,KAAK,EAAE,qBAAqB;SACtC,CAAC,CAAC;QAEH,gDAAgD;QAChD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAClC,CAAC,MAAM,EAAE,EAAE;YACT,eAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBACpC,GAAG,EAAE,MAAM,CAAC,GAAG;gBACf,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,KAAK,EAAE,MAAM,CAAC,IAAI,EAAE,KAAK;aAC1B,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACxD,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CACF,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACnC,CAAC,QAAQ,EAAE,EAAE;YACX,eAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBACrC,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK;aAC5B,CAAC,CAAC;YACH,OAAO,QAAQ,CAAC;QAClB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;gBAC5C,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;gBAC9B,OAAO,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO;aAC9C,CAAC,CAAC;YACH,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAClB,QAAuB,EACvB,OAA4B,EAC5B,UAKI,EAAE;QAEN,IAAI,CAAC;YACH,MAAM,EACJ,KAAK,GAAG,IAAI,CAAC,YAAY,EACzB,WAAW,GAAG,GAAG,EACjB,SAAS,GAAG,IAAI,EAChB,MAAM,GAAG,KAAK,GACf,GAAG,OAAO,CAAC;YAEZ,8CAA8C;YAC9C,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,YAAY,GAAG,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC,CAAC;YAElD,MAAM,QAAQ,GAAkB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC1E,KAAK;gBACL,QAAQ,EAAE,YAAY;gBACtB,WAAW;gBACX,UAAU,EAAE,SAAS;gBACrB,MAAM;gBACN,KAAK,EAAE,GAAG;gBACV,iBAAiB,EAAE,GAAG;gBACtB,gBAAgB,EAAE,GAAG;aACtB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAExC,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;gBAC/B,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK;gBAC1B,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK;gBAC1B,aAAa,EAAE,MAAM,CAAC,aAAa;aACpC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBAC/C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,OAAO;gBACP,QAAQ,EAAE,QAAQ,CAAC,MAAM;aAC1B,CAAC,CAAC;YAEH,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBACnC,MAAM,oBAAW,CAAC,eAAe,CAAC,yDAAyD,CAAC,CAAC;YAC/F,CAAC;iBAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC1C,MAAM,oBAAW,CAAC,YAAY,CAAC,mCAAmC,CAAC,CAAC;YACtE,CAAC;iBAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,IAAI,GAAG,EAAE,CAAC;gBACzC,MAAM,oBAAW,CAAC,mBAAmB,CAAC,qCAAqC,CAAC,CAAC;YAC/E,CAAC;iBAAM,CAAC;gBACN,MAAM,oBAAW,CAAC,UAAU,CAAC,+BAA+B,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,OAA4B;QACrD,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;QAEpD,MAAM,aAAa,GAAG;YACpB,EAAE,EAAE;gBACF,MAAM,EAAE;;;;;;;;;;;;;;qCAcqB;gBAE7B,MAAM,EAAE;;;;;;;;;;;;;;;qCAeqB;aAC9B;YACD,EAAE,EAAE;gBACF,MAAM,EAAE;;;;;;;;;;;;;;6DAc6C;gBAErD,MAAM,EAAE;;;;;;;;;;;;;;;mEAemD;aAC5D;SACF,CAAC;QAEF,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC;YAC1C,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CACpB,MAAc,EACd,UAKI,EAAE;QAEN,MAAM,QAAQ,GAAkB;YAC9B;gBACE,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,MAAM;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB;SACF,CAAC;QAEF,MAAM,OAAO,GAAwB;YACnC,MAAM,EAAE,QAAQ;YAChB,SAAS,EAAE,QAAQ;YACnB,QAAQ,EAAE,QAAQ;YAClB,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,SAAS;YACtB,aAAa,EAAE,EAAE;SAClB,CAAC;QAEF,kEAAkE;QAClE,MAAM,WAAW,GAAG;YAClB,KAAK,EAAE,OAAO,CAAC,KAAgB;YAC/B,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,UAAU;SACnD,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QAC3E,OAAO,QAAQ,CAAC,OAAO,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAClD,eAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE;gBACnD,WAAW,EAAE,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC;aAC9C,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC5E,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAClD,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC;QACnC,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC3E,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;CACF;AAMQ,8CAAiB;AAJ1B,4BAA4B;AACf,QAAA,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC"}