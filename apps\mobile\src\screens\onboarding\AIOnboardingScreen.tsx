/**
 * AI Onboarding Screen
 * AI-powered onboarding system for Syrian freelancers
 * Replaces traditional onboarding with intelligent conversation
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Context & Store
import { useTheme } from '../../contexts/ThemeContext';
import { useAppStore } from '../../store/appStore';
import { authStore } from '../../store/authStore';

// Services
import { phase3AIService } from '../../services/phase3AIService';

// Components
import { Button } from '../../components/common/Button';

// Utils
import { showToast } from '../../utils/toast';

const { width, height } = Dimensions.get('window');

interface AIOnboardingScreenProps {
  navigation: any;
}

const AIOnboardingScreen: React.FC<AIOnboardingScreenProps> = ({ navigation }) => {
  const { currentTheme } = useTheme();
  const { setOnboardingCompleted } = useAppStore();
  const { user } = authStore();
  
  // State
  const [currentStep, setCurrentStep] = useState<'welcome' | 'role_selection' | 'ai_intro' | 'starting'>('welcome');
  const [selectedRole, setSelectedRole] = useState<'CLIENT' | 'EXPERT' | null>(null);
  const [isStarting, setIsStarting] = useState(false);

  /**
   * Handle role selection
   */
  const handleRoleSelection = (role: 'CLIENT' | 'EXPERT') => {
    setSelectedRole(role);
    setCurrentStep('ai_intro');
  };

  /**
   * Start AI onboarding conversation
   */
  const startAIOnboarding = async () => {
    if (!selectedRole) return;

    try {
      setIsStarting(true);
      setCurrentStep('starting');

      // Start AI conversation
      const sessionData = await phase3AIService.startConversation(
        selectedRole,
        'ar',
        'onboarding',
        {
          location: '', // Can be enhanced with user location
          dialect: 'general'
        }
      );

      console.log('✅ AI onboarding session started:', sessionData.sessionId);

      // Navigate to Enhanced AI Chat Screen
      navigation.replace('EnhancedAIChat', {
        sessionId: sessionData.sessionId,
        userRole: selectedRole,
        language: 'ar',
        sessionType: 'onboarding'
      });

    } catch (error: any) {
      console.error('❌ Failed to start AI onboarding:', error);
      setIsStarting(false);
      setCurrentStep('ai_intro');
      
      showToast('error', 'فشل في بدء التسجيل الذكي', 'Failed to start AI onboarding');
      
      Alert.alert(
        'خطأ في الاتصال',
        'فشل في بدء نظام التسجيل الذكي. يرجى المحاولة مرة أخرى أو استخدام التسجيل التقليدي.',
        [
          { text: 'إعادة المحاولة', onPress: startAIOnboarding },
          { text: 'التسجيل التقليدي', onPress: () => setOnboardingCompleted() },
        ]
      );
    }
  };

  /**
   * Skip AI onboarding
   */
  const skipAIOnboarding = () => {
    Alert.alert(
      'تخطي التسجيل الذكي',
      'هل أنت متأكد من تخطي نظام التسجيل الذكي؟ سيساعدك هذا النظام في إعداد ملفك الشخصي بشكل أفضل.',
      [
        { text: 'العودة', style: 'cancel' },
        { text: 'تخطي', onPress: () => setOnboardingCompleted() },
      ]
    );
  };

  /**
   * Render welcome step
   */
  const renderWelcomeStep = () => (
    <View style={styles.stepContainer}>
      {/* AI Icon */}
      <LinearGradient
        colors={[currentTheme.colors.primary[500] + '20', currentTheme.colors.primary[500] + '10']}
        style={styles.iconContainer}
      >
        <Icon
          name="psychology"
          size={80}
          color={currentTheme.colors.primary[500]}
        />
      </LinearGradient>

      {/* Title */}
      <Text style={[styles.title, { color: currentTheme.colors.text.primary }]}>
        مرحباً بك في فريلا سوريا
      </Text>

      {/* Description */}
      <Text style={[styles.description, { color: currentTheme.colors.text.secondary }]}>
        نظام التسجيل الذكي المدعوم بالذكاء الاصطناعي{'\n'}
        سيساعدك في إعداد ملفك الشخصي بطريقة تفاعلية وذكية
      </Text>

      {/* Features */}
      <View style={styles.featuresContainer}>
        <FeatureItem
          icon="mic"
          title="التفاعل الصوتي"
          description="تحدث بصوتك واللهجة السورية"
          theme={currentTheme}
        />
        <FeatureItem
          icon="image"
          title="تحليل الأعمال"
          description="ارفع صور أعمالك للتحليل الذكي"
          theme={currentTheme}
        />
        <FeatureItem
          icon="psychology"
          title="مساعد ذكي"
          description="مساعد يفهم السوق السوري"
          theme={currentTheme}
        />
      </View>

      {/* Continue Button */}
      <Button
        title="ابدأ التسجيل الذكي"
        onPress={() => setCurrentStep('role_selection')}
        style={styles.continueButton}
      />

      {/* Skip Button */}
      <TouchableOpacity
        style={styles.skipButton}
        onPress={skipAIOnboarding}
      >
        <Text style={[styles.skipText, { color: currentTheme.colors.text.muted }]}>
          تخطي والانتقال للتسجيل التقليدي
        </Text>
      </TouchableOpacity>
    </View>
  );

  /**
   * Render role selection step
   */
  const renderRoleSelectionStep = () => (
    <View style={styles.stepContainer}>
      <Text style={[styles.title, { color: currentTheme.colors.text.primary }]}>
        اختر دورك في المنصة
      </Text>

      <Text style={[styles.description, { color: currentTheme.colors.text.secondary }]}>
        سيساعدنا هذا في تخصيص تجربة التسجيل لك
      </Text>

      {/* Role Options */}
      <View style={styles.roleContainer}>
        <RoleCard
          icon="work"
          title="أنا خبير"
          description="أقدم خدمات وأبحث عن مشاريع"
          role="EXPERT"
          selected={selectedRole === 'EXPERT'}
          onSelect={handleRoleSelection}
          theme={currentTheme}
        />

        <RoleCard
          icon="business"
          title="أنا عميل"
          description="أبحث عن خبراء لمشاريعي"
          role="CLIENT"
          selected={selectedRole === 'CLIENT'}
          onSelect={handleRoleSelection}
          theme={currentTheme}
        />
      </View>

      {/* Back Button */}
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => setCurrentStep('welcome')}
      >
        <Icon name="arrow-back" size={20} color={currentTheme.colors.text.secondary} />
        <Text style={[styles.backText, { color: currentTheme.colors.text.secondary }]}>
          العودة
        </Text>
      </TouchableOpacity>
    </View>
  );

  /**
   * Render AI intro step
   */
  const renderAIIntroStep = () => (
    <View style={styles.stepContainer}>
      <LinearGradient
        colors={[currentTheme.colors.primary[500] + '20', currentTheme.colors.primary[500] + '10']}
        style={styles.iconContainer}
      >
        <Icon
          name="chat"
          size={80}
          color={currentTheme.colors.primary[500]}
        />
      </LinearGradient>

      <Text style={[styles.title, { color: currentTheme.colors.text.primary }]}>
        {selectedRole === 'EXPERT' ? 'مساعد الخبراء الذكي' : 'مساعد العملاء الذكي'}
      </Text>

      <Text style={[styles.description, { color: currentTheme.colors.text.secondary }]}>
        {selectedRole === 'EXPERT' 
          ? 'سأساعدك في إعداد ملفك المهني وتحديد مهاراتك وخدماتك بطريقة تفاعلية'
          : 'سأساعدك في تحديد احتياجاتك ومتطلبات مشاريعك للعثور على أفضل الخبراء'
        }
      </Text>

      {/* What to expect */}
      <View style={styles.expectationContainer}>
        <Text style={[styles.expectationTitle, { color: currentTheme.colors.text.primary }]}>
          ما يمكنك توقعه:
        </Text>
        
        {selectedRole === 'EXPERT' ? (
          <>
            <ExpectationItem text="أسئلة حول مهاراتك وخبراتك" theme={currentTheme} />
            <ExpectationItem text="تحليل أعمالك السابقة" theme={currentTheme} />
            <ExpectationItem text="اقتراحات لتحسين ملفك" theme={currentTheme} />
            <ExpectationItem text="تحديد أسعارك المناسبة" theme={currentTheme} />
          </>
        ) : (
          <>
            <ExpectationItem text="أسئلة حول مشاريعك" theme={currentTheme} />
            <ExpectationItem text="تحديد نوع الخبراء المطلوبين" theme={currentTheme} />
            <ExpectationItem text="اقتراحات لميزانيتك" theme={currentTheme} />
            <ExpectationItem text="نصائح للتعامل مع الخبراء" theme={currentTheme} />
          </>
        )}
      </View>

      {/* Start Button */}
      <Button
        title="ابدأ المحادثة الآن"
        onPress={startAIOnboarding}
        style={styles.startButton}
        disabled={isStarting}
      />

      {/* Back Button */}
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => setCurrentStep('role_selection')}
        disabled={isStarting}
      >
        <Icon name="arrow-back" size={20} color={currentTheme.colors.text.secondary} />
        <Text style={[styles.backText, { color: currentTheme.colors.text.secondary }]}>
          تغيير الدور
        </Text>
      </TouchableOpacity>
    </View>
  );

  /**
   * Render starting step
   */
  const renderStartingStep = () => (
    <View style={styles.stepContainer}>
      <ActivityIndicator size="large" color={currentTheme.colors.primary[500]} />
      <Text style={[styles.title, { color: currentTheme.colors.text.primary }]}>
        جاري بدء المحادثة الذكية...
      </Text>
      <Text style={[styles.description, { color: currentTheme.colors.text.secondary }]}>
        يرجى الانتظار بينما نعد المساعد الذكي لك
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: currentTheme.colors.background }]}>
      <LinearGradient
        colors={[
          currentTheme.colors.background,
          currentTheme.colors.background + 'F0'
        ]}
        style={styles.gradient}
      >
        {currentStep === 'welcome' && renderWelcomeStep()}
        {currentStep === 'role_selection' && renderRoleSelectionStep()}
        {currentStep === 'ai_intro' && renderAIIntroStep()}
        {currentStep === 'starting' && renderStartingStep()}
      </LinearGradient>
    </SafeAreaView>
  );
};

// Feature Item Component
const FeatureItem: React.FC<{
  icon: string;
  title: string;
  description: string;
  theme: any;
}> = ({ icon, title, description, theme }) => (
  <View style={styles.featureItem}>
    <Icon name={icon} size={24} color={theme.colors.primary[500]} />
    <View style={styles.featureText}>
      <Text style={[styles.featureTitle, { color: theme.colors.text.primary }]}>
        {title}
      </Text>
      <Text style={[styles.featureDescription, { color: theme.colors.text.secondary }]}>
        {description}
      </Text>
    </View>
  </View>
);

// Role Card Component
const RoleCard: React.FC<{
  icon: string;
  title: string;
  description: string;
  role: 'CLIENT' | 'EXPERT';
  selected: boolean;
  onSelect: (role: 'CLIENT' | 'EXPERT') => void;
  theme: any;
}> = ({ icon, title, description, role, selected, onSelect, theme }) => (
  <TouchableOpacity
    style={[
      styles.roleCard,
      {
        backgroundColor: theme.colors.glass.background,
        borderColor: selected ? theme.colors.primary[500] : theme.colors.glass.border,
        borderWidth: selected ? 2 : 1,
      }
    ]}
    onPress={() => onSelect(role)}
    activeOpacity={0.8}
  >
    <Icon
      name={icon}
      size={48}
      color={selected ? theme.colors.primary[500] : theme.colors.text.secondary}
    />
    <Text style={[
      styles.roleTitle,
      { color: selected ? theme.colors.primary[500] : theme.colors.text.primary }
    ]}>
      {title}
    </Text>
    <Text style={[styles.roleDescription, { color: theme.colors.text.secondary }]}>
      {description}
    </Text>
  </TouchableOpacity>
);

// Expectation Item Component
const ExpectationItem: React.FC<{ text: string; theme: any }> = ({ text, theme }) => (
  <View style={styles.expectationItem}>
    <Icon name="check-circle" size={16} color={theme.colors.primary[500]} />
    <Text style={[styles.expectationText, { color: theme.colors.text.secondary }]}>
      {text}
    </Text>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  stepContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  iconContainer: {
    width: 160,
    height: 160,
    borderRadius: 80,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    fontFamily: 'Cairo-Bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    fontFamily: 'Cairo-Regular',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  featuresContainer: {
    width: '100%',
    marginBottom: 32,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  featureText: {
    marginLeft: 12,
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontFamily: 'Cairo-SemiBold',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    fontFamily: 'Cairo-Regular',
  },
  roleContainer: {
    width: '100%',
    marginBottom: 32,
  },
  roleCard: {
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  roleTitle: {
    fontSize: 20,
    fontFamily: 'Cairo-Bold',
    marginTop: 12,
    marginBottom: 8,
  },
  roleDescription: {
    fontSize: 14,
    fontFamily: 'Cairo-Regular',
    textAlign: 'center',
  },
  expectationContainer: {
    width: '100%',
    marginBottom: 32,
  },
  expectationTitle: {
    fontSize: 18,
    fontFamily: 'Cairo-SemiBold',
    marginBottom: 16,
    textAlign: 'center',
  },
  expectationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  expectationText: {
    fontSize: 14,
    fontFamily: 'Cairo-Regular',
    marginLeft: 8,
    flex: 1,
  },
  continueButton: {
    width: '100%',
    marginBottom: 16,
  },
  startButton: {
    width: '100%',
    marginBottom: 16,
  },
  skipButton: {
    padding: 12,
  },
  skipText: {
    fontSize: 14,
    fontFamily: 'Cairo-Regular',
    textAlign: 'center',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  backText: {
    fontSize: 14,
    fontFamily: 'Cairo-Regular',
    marginLeft: 8,
  },
});

export default AIOnboardingScreen;
