/**
 * Phase 3 AI Service - Advanced AI Features Integration
 * Real-time AI conversation management with Syrian cultural context
 */

import { supabase } from '@freela/database/src/supabase';
import { openRouterService } from '../openrouter';
import { logger } from '../../utils/logger';
import { createError } from '../../utils/errors';

export interface Phase3AISession {
  id: string;
  userId: string;
  userRole: 'CLIENT' | 'EXPERT';
  sessionType: 'onboarding' | 'profile_optimization' | 'service_creation' | 'skill_assessment';
  language: 'ar' | 'en';
  culturalContext: {
    country: string;
    language: string;
    dialect: string;
    location: string;
    marketContext: string;
    economicContext: string;
    culturalNotes: string[];
  };
  currentStep: string;
  totalSteps: number;
  completionRate: number;
  status: 'active' | 'completed' | 'paused' | 'abandoned';
  extractedData: any;
  aiModel: string;
  features: {
    realTimeProcessing: boolean;
    skillExtraction: boolean;
    marketIntelligence: boolean;
    culturalAdaptation: boolean;
    voiceSupport: boolean;
    imageAnalysis: boolean;
  };
  createdAt: Date;
  lastActivity: Date;
}

export interface AIMessage {
  id: string;
  sessionId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  messageType: 'text' | 'voice_transcript' | 'image_description' | 'welcome' | 'response' | 'analysis';
  confidenceScore?: number;
  processingTime?: number;
  extractedData?: any;
  metadata?: any;
  createdAt: Date;
}

export interface SyrianMarketContext {
  averagePrices: {
    webDevelopment: { min: number; max: number; currency: string };
    graphicDesign: { min: number; max: number; currency: string };
    contentWriting: { min: number; max: number; currency: string };
    digitalMarketing: { min: number; max: number; currency: string };
    translation: { min: number; max: number; currency: string };
  };
  demandLevels: {
    [skill: string]: 'low' | 'medium' | 'high';
  };
  culturalConsiderations: string[];
  economicFactors: string[];
}

export class Phase3AIService {
  private syrianMarketData: SyrianMarketContext;

  constructor() {
    this.syrianMarketData = {
      averagePrices: {
        webDevelopment: { min: 15, max: 50, currency: 'USD' },
        graphicDesign: { min: 10, max: 30, currency: 'USD' },
        contentWriting: { min: 5, max: 20, currency: 'USD' },
        digitalMarketing: { min: 12, max: 40, currency: 'USD' },
        translation: { min: 8, max: 25, currency: 'USD' }
      },
      demandLevels: {
        'web_development': 'high',
        'mobile_development': 'high',
        'graphic_design': 'medium',
        'content_writing': 'medium',
        'digital_marketing': 'high',
        'translation': 'medium',
        'video_editing': 'medium',
        'data_entry': 'low'
      },
      culturalConsiderations: [
        'Respectful and professional communication',
        'Family-oriented work-life balance',
        'Religious considerations for work schedules',
        'Local market pricing awareness',
        'Regional skill demands understanding'
      ],
      economicFactors: [
        'Developing economy with focus on digital services',
        'Growing demand for online freelance work',
        'Competitive pricing due to economic conditions',
        'Emphasis on quality to compete internationally'
      ]
    };
  }

  /**
   * Start a new Phase 3 AI conversation session
   */
  async startConversation(
    userId: string,
    userRole: 'CLIENT' | 'EXPERT',
    sessionType: string,
    language: 'ar' | 'en' = 'ar',
    culturalContext: any = {}
  ): Promise<Phase3AISession> {
    try {
      const sessionData = {
        user_id: userId,
        user_role: userRole,
        session_type: sessionType,
        language,
        cultural_context: {
          country: 'Syria',
          language: language === 'ar' ? 'Arabic' : 'English',
          dialect: culturalContext.dialect || 'general',
          location: culturalContext.location || '',
          marketContext: 'Syrian freelance market',
          economicContext: 'Developing economy with focus on digital services',
          culturalNotes: this.syrianMarketData.culturalConsiderations
        },
        current_step: 'welcome',
        total_steps: this.getTotalSteps(sessionType),
        completion_rate: 0,
        status: 'active',
        extracted_data: {},
        ai_model: 'openai/gpt-4-turbo-preview',
        features_enabled: {
          realTimeProcessing: true,
          skillExtraction: true,
          marketIntelligence: true,
          culturalAdaptation: true,
          voiceSupport: true,
          imageAnalysis: true
        }
      };

      const { data: session, error } = await supabase
        .from('ai_chat_sessions')
        .insert(sessionData)
        .select()
        .single();

      if (error) {
        logger.error('Failed to create Phase 3 AI session', { error, userId });
        throw createError.internalServerError('Failed to create conversation session');
      }

      logger.info('Phase 3 AI session created', {
        userId,
        sessionId: session.id,
        userRole,
        sessionType,
        language
      });

      return this.mapSessionFromDB(session);

    } catch (error) {
      logger.error('Error starting Phase 3 AI conversation', { error, userId });
      throw error;
    }
  }

  /**
   * Generate culturally-aware welcome message
   */
  async generateWelcomeMessage(
    session: Phase3AISession
  ): Promise<string> {
    const welcomePrompt = this.buildWelcomePrompt(session);

    try {
      const response = await openRouterService.chatCompletion(
        [{ role: 'user', content: welcomePrompt }],
        {
          culturalContext: session.culturalContext,
          sessionType: session.sessionType,
          userRole: session.userRole
        },
        {
          temperature: 0.8,
          maxTokens: 300,
          model: session.aiModel
        }
      );

      // Store welcome message
      await this.storeMessage(
        session.id,
        'assistant',
        response.content,
        'welcome',
        { confidenceScore: 0.95 }
      );

      return response.content;

    } catch (error) {
      logger.error('Failed to generate welcome message', { error, sessionId: session.id });
      throw createError.internalServerError('Failed to generate welcome message');
    }
  }

  /**
   * Process user message with advanced AI analysis
   */
  async processMessage(
    sessionId: string,
    userId: string,
    message: string,
    messageType: string = 'text',
    metadata: any = {}
  ): Promise<{
    aiResponse: string;
    extractedData: any;
    nextStep: string;
    completionRate: number;
  }> {
    try {
      // Get session
      const session = await this.getSession(sessionId, userId);
      if (!session) {
        throw createError.notFound('Session not found');
      }

      // Store user message
      await this.storeMessage(sessionId, 'user', message, messageType, { metadata });

      // Get conversation history
      const messageHistory = await this.getMessageHistory(sessionId, 20);

      // Build conversation context
      const conversationMessages = this.buildConversationMessages(
        session,
        messageHistory,
        message
      );

      // Get AI response
      const startTime = Date.now();
      const aiResponse = await openRouterService.chatCompletion(
        conversationMessages,
        {
          culturalContext: session.culturalContext,
          sessionType: session.sessionType,
          userRole: session.userRole,
          currentStep: session.currentStep,
          marketData: this.syrianMarketData
        },
        {
          temperature: 0.7,
          maxTokens: 800,
          model: session.aiModel
        }
      );
      const processingTime = Date.now() - startTime;

      // Extract data from conversation
      const extractedData = await this.extractDataFromMessage(
        session,
        message,
        aiResponse.content
      );

      // Determine next step
      const nextStep = this.determineNextStep(session, extractedData);
      const completionRate = this.calculateCompletionRate(nextStep, session.totalSteps);

      // Store AI response
      await this.storeMessage(
        sessionId,
        'assistant',
        aiResponse.content,
        'response',
        {
          confidenceScore: 0.8,
          processingTime,
          extractedData
        }
      );

      // Update session
      await this.updateSession(sessionId, {
        current_step: nextStep,
        completion_rate: completionRate,
        extracted_data: {
          ...session.extractedData,
          ...extractedData,
          last_update: new Date().toISOString()
        },
        status: completionRate >= 100 ? 'completed' : 'active',
        last_activity: new Date().toISOString()
      });

      logger.info('Phase 3 message processed', {
        sessionId,
        userId,
        messageType,
        processingTime,
        nextStep,
        completionRate
      });

      return {
        aiResponse: aiResponse.content,
        extractedData,
        nextStep,
        completionRate
      };

    } catch (error) {
      logger.error('Failed to process Phase 3 message', { error, sessionId, userId });
      throw error;
    }
  }

  /**
   * Get session by ID and user ID
   */
  async getSession(sessionId: string, userId: string): Promise<Phase3AISession | null> {
    try {
      const { data: session, error } = await supabase
        .from('ai_chat_sessions')
        .select('*')
        .eq('id', sessionId)
        .eq('user_id', userId)
        .single();

      if (error || !session) {
        return null;
      }

      return this.mapSessionFromDB(session);

    } catch (error) {
      logger.error('Failed to get Phase 3 session', { error, sessionId, userId });
      return null;
    }
  }

  /**
   * Store message in database
   */
  private async storeMessage(
    sessionId: string,
    role: 'user' | 'assistant' | 'system',
    content: string,
    messageType: string,
    options: any = {}
  ): Promise<void> {
    try {
      await supabase
        .from('ai_chat_messages')
        .insert({
          session_id: sessionId,
          role,
          content,
          message_type: messageType,
          confidence_score: options.confidenceScore || null,
          processing_time: options.processingTime || null,
          extracted_data: options.extractedData || null,
          metadata: options.metadata || null
        });

    } catch (error) {
      logger.error('Failed to store message', { error, sessionId });
      throw error;
    }
  }

  // Helper methods will be added in the next part...
  private getTotalSteps(sessionType: string): number {
    const stepCounts = {
      'onboarding': 8,
      'profile_optimization': 5,
      'service_creation': 6,
      'skill_assessment': 4
    };
    return stepCounts[sessionType] || 5;
  }

  private mapSessionFromDB(dbSession: any): Phase3AISession {
    return {
      id: dbSession.id,
      userId: dbSession.user_id,
      userRole: dbSession.user_role,
      sessionType: dbSession.session_type,
      language: dbSession.language,
      culturalContext: dbSession.cultural_context,
      currentStep: dbSession.current_step,
      totalSteps: dbSession.total_steps,
      completionRate: dbSession.completion_rate,
      status: dbSession.status,
      extractedData: dbSession.extracted_data,
      aiModel: dbSession.ai_model,
      features: dbSession.features_enabled,
      createdAt: new Date(dbSession.created_at),
      lastActivity: new Date(dbSession.last_activity || dbSession.created_at)
    };
  }
}

  /**
   * Get message history for a session
   */
  private async getMessageHistory(sessionId: string, limit: number = 20): Promise<AIMessage[]> {
    try {
      const { data: messages, error } = await supabase
        .from('ai_chat_messages')
        .select('*')
        .eq('session_id', sessionId)
        .order('created_at', { ascending: true })
        .limit(limit);

      if (error) {
        logger.error('Failed to get message history', { error, sessionId });
        return [];
      }

      return messages?.map(msg => ({
        id: msg.id,
        sessionId: msg.session_id,
        role: msg.role,
        content: msg.content,
        messageType: msg.message_type,
        confidenceScore: msg.confidence_score,
        processingTime: msg.processing_time,
        extractedData: msg.extracted_data,
        metadata: msg.metadata,
        createdAt: new Date(msg.created_at)
      })) || [];

    } catch (error) {
      logger.error('Error getting message history', { error, sessionId });
      return [];
    }
  }

  /**
   * Build conversation messages with system prompt
   */
  private buildConversationMessages(
    session: Phase3AISession,
    messageHistory: AIMessage[],
    currentMessage: string
  ): Array<{ role: 'system' | 'user' | 'assistant'; content: string }> {
    const systemPrompt = this.buildSystemPrompt(session);

    const messages = [
      { role: 'system' as const, content: systemPrompt }
    ];

    // Add recent message history
    messageHistory.forEach(msg => {
      if (msg.role !== 'system') {
        messages.push({
          role: msg.role as 'user' | 'assistant',
          content: msg.content
        });
      }
    });

    // Add current message
    messages.push({
      role: 'user' as const,
      content: currentMessage
    });

    return messages;
  }

  /**
   * Build system prompt with Syrian cultural context
   */
  private buildSystemPrompt(session: Phase3AISession): string {
    const roleText = session.userRole === 'EXPERT' ? 'الخبراء' : 'العملاء';
    const sessionTypeText = {
      'onboarding': 'إعداد الملف الشخصي',
      'profile_optimization': 'تحسين الملف الشخصي',
      'service_creation': 'إنشاء الخدمات',
      'skill_assessment': 'تقييم المهارات'
    }[session.sessionType] || 'المساعدة العامة';

    return `أنت مساعد ذكي متخصص في منصة فريلا سوريا للعمل الحر. تساعد ${roleText} السوريين في ${sessionTypeText}.

السياق الثقافي والجغرافي:
- البلد: ${session.culturalContext.country}
- اللغة: ${session.culturalContext.language}
- اللهجة: ${session.culturalContext.dialect}
- الموقع: ${session.culturalContext.location || 'مختلف المناطق السورية'}
- السوق المستهدف: ${session.culturalContext.marketContext}
- السياق الاقتصادي: ${session.culturalContext.economicContext}

المرحلة الحالية: ${session.currentStep}
نسبة الإنجاز: ${session.completionRate}%

الاعتبارات الثقافية:
${session.culturalContext.culturalNotes.map(note => `- ${note}`).join('\n')}

معلومات السوق السوري:
- متوسط أسعار تطوير الويب: $15-50 للساعة
- متوسط أسعار التصميم الجرافيكي: $10-30 للساعة
- متوسط أسعار كتابة المحتوى: $5-20 للساعة
- متوسط أسعار التسويق الرقمي: $12-40 للساعة
- متوسط أسعار الترجمة: $8-25 للساعة

مهامك الأساسية:
1. تحليل رسائل المستخدم واستخراج المعلومات المهمة
2. تقديم نصائح مخصصة للسوق السوري
3. مساعدة في تحديد الأسعار المناسبة للسوق المحلي
4. تقديم اقتراحات تتماشى مع الثقافة السورية
5. استخدام اللغة العربية الواضحة مع تعابير محلية مناسبة

إرشادات الاستجابة:
- كن مهنياً ومحترماً ومشجعاً
- راعي الظروف الاقتصادية في سوريا
- اقترح حلول عملية وقابلة للتطبيق
- استخدم أمثلة من السوق السوري عند الإمكان
- كن متفائلاً وداعماً للمستخدم
- اطرح أسئلة توضيحية عند الحاجة`;
  }

  /**
   * Build welcome prompt for new sessions
   */
  private buildWelcomePrompt(session: Phase3AISession): string {
    const roleText = session.userRole === 'EXPERT' ? 'خبير' : 'عميل';
    const sessionTypeText = {
      'onboarding': 'إعداد ملفك الشخصي',
      'profile_optimization': 'تحسين ملفك الشخصي',
      'service_creation': 'إنشاء خدماتك',
      'skill_assessment': 'تقييم مهاراتك'
    }[session.sessionType] || 'المساعدة';

    return `أنت مساعد ذكي لمنصة فريلا سوريا. مهمتك الترحيب بـ${roleText} جديد وبدء عملية ${sessionTypeText}.

السياق:
- المستخدم ${roleText} من سوريا
- يريد ${sessionTypeText} على منصة فريلا سوريا
- اللغة المفضلة: ${session.language === 'ar' ? 'العربية' : 'الإنجليزية'}
- المنطقة: ${session.culturalContext.location || 'سوريا'}

اكتب رسالة ترحيب دافئة ومهنية تتضمن:
1. ترحيب حار بالمستخدم
2. شرح مختصر لكيفية مساعدتك له
3. سؤال أولي لبدء المحادثة
4. تشجيع وتحفيز

استخدم اللغة العربية مع مراعاة الثقافة السورية. كن ودوداً ومشجعاً.`;
  }

  /**
   * Extract data from conversation using AI
   */
  private async extractDataFromMessage(
    session: Phase3AISession,
    userMessage: string,
    aiResponse: string
  ): Promise<any> {
    const extractionPrompt = `
تحليل المحادثة التالية واستخراج المعلومات المهمة:

رسالة المستخدم: ${userMessage}
رد المساعد: ${aiResponse}

المرحلة الحالية: ${session.currentStep}
نوع الجلسة: ${session.sessionType}

استخرج المعلومات التالية إذا كانت متوفرة وأجب بصيغة JSON فقط:
{
  "skills": [],
  "experience_years": null,
  "project_types": [],
  "pricing_preferences": {},
  "location_details": "",
  "work_preferences": [],
  "challenges_mentioned": [],
  "goals": [],
  "portfolio_items": [],
  "availability": "",
  "confidence_score": 0.0
}`;

    try {
      const response = await openRouterService.chatCompletion(
        [{ role: 'user', content: extractionPrompt }],
        { extractionMode: true },
        {
          temperature: 0.3,
          maxTokens: 500,
          model: 'openai/gpt-4-turbo-preview'
        }
      );

      return JSON.parse(response.content);

    } catch (error) {
      logger.warn('Failed to extract data from message', { error, sessionId: session.id });
      return { confidence_score: 0.5 };
    }
  }

  /**
   * Determine next step based on current progress
   */
  private determineNextStep(session: Phase3AISession, extractedData: any): string {
    const currentStep = session.currentStep;
    const sessionType = session.sessionType;

    if (sessionType === 'onboarding') {
      const steps = [
        'welcome',
        'personal_info',
        'skills_assessment',
        'experience_review',
        'portfolio_analysis',
        'pricing_strategy',
        'market_positioning',
        'profile_optimization',
        'completion'
      ];

      const currentIndex = steps.indexOf(currentStep);
      const hasEnoughData = this.checkStepCompletion(currentStep, extractedData);

      if (hasEnoughData && currentIndex < steps.length - 1) {
        return steps[currentIndex + 1];
      }
    }

    return currentStep;
  }

  /**
   * Check if current step has enough data to proceed
   */
  private checkStepCompletion(step: string, extractedData: any): boolean {
    switch (step) {
      case 'welcome':
        return true;
      case 'personal_info':
        return extractedData.location_details || extractedData.work_preferences?.length > 0;
      case 'skills_assessment':
        return extractedData.skills?.length > 0;
      case 'experience_review':
        return extractedData.experience_years !== null || extractedData.project_types?.length > 0;
      case 'portfolio_analysis':
        return extractedData.portfolio_items?.length > 0;
      case 'pricing_strategy':
        return Object.keys(extractedData.pricing_preferences || {}).length > 0;
      default:
        return true;
    }
  }

  /**
   * Calculate completion rate based on current step
   */
  private calculateCompletionRate(currentStep: string, totalSteps: number): number {
    const steps = [
      'welcome',
      'personal_info',
      'skills_assessment',
      'experience_review',
      'portfolio_analysis',
      'pricing_strategy',
      'market_positioning',
      'profile_optimization',
      'completion'
    ];

    const currentIndex = steps.indexOf(currentStep);
    return Math.round((currentIndex / (totalSteps - 1)) * 100);
  }

  /**
   * Update session in database
   */
  private async updateSession(sessionId: string, updates: any): Promise<void> {
    try {
      const { error } = await supabase
        .from('ai_chat_sessions')
        .update(updates)
        .eq('id', sessionId);

      if (error) {
        logger.error('Failed to update session', { error, sessionId });
        throw error;
      }

    } catch (error) {
      logger.error('Error updating session', { error, sessionId });
      throw error;
    }
  }
}

export const phase3AIService = new Phase3AIService();
