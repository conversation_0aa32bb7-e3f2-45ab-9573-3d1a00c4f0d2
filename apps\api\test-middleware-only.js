console.log('Testing middleware initialization only...');

try {
  console.log('Loading App class...');
  const App = require('./dist/app.js').default;
  console.log('✅ App class loaded');
  
  console.log('Creating app instance...');
  const app = new App();
  console.log('✅ App instance created');
  
  console.log('Testing initializeMiddleware directly...');
  
  // Access the private method through the prototype
  const initializeMiddleware = app.constructor.prototype.initializeMiddleware;
  
  // Try to call it directly
  try {
    initializeMiddleware.call(app);
    console.log('✅ Middleware initialized successfully');
    
    console.log('Starting server...');
    const server = app.app.listen(3001, () => {
      console.log('🚀 Server running on port 3001');
      console.log('🏥 Health: http://localhost:3001/health');
      
      // Test the health endpoint
      setTimeout(() => {
        console.log('Testing health endpoint...');
        fetch('http://localhost:3001/health')
          .then(res => res.json())
          .then(data => {
            console.log('✅ Health check response:', data);
            server.close();
          })
          .catch(err => {
            console.error('❌ Health check failed:', err);
            server.close();
          });
      }, 1000);
    });
    
  } catch (middlewareError) {
    console.error('❌ Middleware initialization failed:');
    console.error('Error message:', middlewareError.message);
    console.error('Error stack:', middlewareError.stack);
  }
  
} catch (error) {
  console.error('❌ Error during setup:');
  console.error('Error message:', error.message);
  console.error('Error stack:', error.stack);
}
