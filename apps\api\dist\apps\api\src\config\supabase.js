"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.supabase = void 0;
const supabase_js_1 = require("@supabase/supabase-js");
const index_1 = require("./index");
const logger_1 = require("../utils/logger");
// Create Supabase client
let supabase = null;
exports.supabase = supabase;
try {
    if (index_1.supabaseConfig.url && index_1.supabaseConfig.serviceRoleKey) {
        exports.supabase = supabase = (0, supabase_js_1.createClient)(index_1.supabaseConfig.url, index_1.supabaseConfig.serviceRoleKey, {
            auth: {
                autoRefreshToken: false,
                persistSession: false
            }
        });
        logger_1.logger.info('✅ Supabase client initialized successfully', {
            url: index_1.supabaseConfig.url,
            hasServiceKey: !!index_1.supabaseConfig.serviceRoleKey
        });
    }
    else {
        logger_1.logger.warn('⚠️ Supabase configuration missing, creating mock client');
        // Create a mock client for development
        exports.supabase = supabase = {
            from: (table) => ({
                select: () => ({
                    eq: () => ({
                        single: () => Promise.resolve({ data: null, error: new Error('Supabase not configured') }),
                        limit: () => Promise.resolve({ data: [], error: null })
                    }),
                    order: () => ({
                        limit: () => Promise.resolve({ data: [], error: null })
                    })
                }),
                insert: () => ({
                    select: () => ({
                        single: () => Promise.resolve({ data: { id: 'mock-id' }, error: null })
                    })
                }),
                update: () => ({
                    eq: () => Promise.resolve({ error: null })
                })
            })
        };
    }
}
catch (error) {
    logger_1.logger.error('❌ Failed to initialize Supabase client', { error });
    // Create a mock client as fallback
    exports.supabase = supabase = {
        from: (table) => ({
            select: () => ({
                eq: () => ({
                    single: () => Promise.resolve({ data: null, error: new Error('Supabase initialization failed') }),
                    limit: () => Promise.resolve({ data: [], error: null })
                }),
                order: () => ({
                    limit: () => Promise.resolve({ data: [], error: null })
                })
            }),
            insert: () => ({
                select: () => ({
                    single: () => Promise.resolve({ data: { id: 'mock-id' }, error: null })
                })
            }),
            update: () => ({
                eq: () => Promise.resolve({ error: null })
            })
        })
    };
}
exports.default = supabase;
//# sourceMappingURL=supabase.js.map