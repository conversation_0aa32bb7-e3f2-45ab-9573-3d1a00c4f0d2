console.log('Starting server test...');

try {
  console.log('Loading config...');
  const config = require('./dist/config/index.js');
  console.log('✅ Config loaded');
  
  console.log('Loading app...');
  const App = require('./dist/app.js').default;
  console.log('✅ App loaded');
  
  console.log('Creating app instance...');
  const app = new App();
  console.log('✅ App instance created');
  
  console.log('Initializing app...');
  app.initialize().then(() => {
    console.log('✅ App initialized');
    
    const server = app.app.listen(3001, () => {
      console.log('🚀 Server running on port 3001');
      console.log('🏥 Health: http://localhost:3001/health');
    });
    
  }).catch(error => {
    console.error('❌ App initialization failed:', error);
  });
  
} catch (error) {
  console.error('❌ Error:', error);
}
