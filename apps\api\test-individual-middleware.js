console.log('Testing individual middleware...');

try {
  const express = require('express');
  const security = require('./dist/middleware/security.js');
  
  console.log('Creating express app...');
  const app = express();
  
  const middlewares = [
    'requestId',
    'securityHeaders', 
    'helmet',
    'cors',
    'compression',
    'suspiciousActivityDetection',
    'rateLimit'
  ];
  
  console.log('\nTesting each middleware individually:');
  
  middlewares.forEach(name => {
    try {
      const middleware = security.default[name];
      console.log(`Testing ${name}:`, typeof middleware);
      
      if (typeof middleware === 'function') {
        // Try to use the middleware
        app.use(middleware);
        console.log(`✅ ${name} - Successfully added to app`);
      } else {
        console.log(`❌ ${name} - Not a function (${typeof middleware})`);
      }
    } catch (error) {
      console.log(`❌ ${name} - Error:`, error.message);
    }
  });
  
  console.log('\n✅ All middleware tests completed');
  
} catch (error) {
  console.error('❌ Error during middleware testing:', error);
}
