/**
 * Arabic Voice Recognition Service for Freela Syria
 * Supports Syrian dialect and formal Arabic with AI-powered transcription
 * Enhanced with Phase 3 AI integration for real-time conversation processing
 */

import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';
import { openRouterService } from './openRouterService';
import { phase3AIService, VoiceProcessingResult } from './phase3AIService';

export interface VoiceRecognitionResult {
  transcript: string;
  confidence: number;
  language: 'ar' | 'ar-sy' | 'en';
  dialect: 'formal' | 'syrian' | 'mixed';
  extractedData?: {
    skills?: string[];
    services?: string[];
    experience?: string;
    pricing?: number;
  };
}

export interface VoiceRecognitionOptions {
  language?: 'ar' | 'en' | 'auto';
  maxDuration?: number; // in seconds
  enableDialectDetection?: boolean;
  enableDataExtraction?: boolean;
}

export class VoiceRecognitionService {
  private recording: Audio.Recording | null = null;
  private isRecording = false;
  private recordingUri: string | null = null;

  /**
   * Start voice recording
   */
  async startRecording(options: VoiceRecognitionOptions = {}): Promise<void> {
    try {
      // Request permissions
      const permission = await Audio.requestPermissionsAsync();
      if (permission.status !== 'granted') {
        throw new Error('Audio recording permission not granted');
      }

      // Configure audio mode
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });

      // Create recording
      this.recording = new Audio.Recording();
      
      const recordingOptions = {
        android: {
          extension: '.wav',
          outputFormat: Audio.RECORDING_OPTION_ANDROID_OUTPUT_FORMAT_DEFAULT,
          audioEncoder: Audio.RECORDING_OPTION_ANDROID_AUDIO_ENCODER_DEFAULT,
          sampleRate: 16000,
          numberOfChannels: 1,
          bitRate: 128000,
        },
        ios: {
          extension: '.wav',
          outputFormat: Audio.RECORDING_OPTION_IOS_OUTPUT_FORMAT_LINEARPCM,
          audioQuality: Audio.RECORDING_OPTION_IOS_AUDIO_QUALITY_HIGH,
          sampleRate: 16000,
          numberOfChannels: 1,
          bitRate: 128000,
          linearPCMBitDepth: 16,
          linearPCMIsBigEndian: false,
          linearPCMIsFloat: false,
        },
      };

      await this.recording.prepareToRecordAsync(recordingOptions);
      await this.recording.startAsync();
      
      this.isRecording = true;

      // Auto-stop after max duration
      if (options.maxDuration) {
        setTimeout(() => {
          if (this.isRecording) {
            this.stopRecording();
          }
        }, options.maxDuration * 1000);
      }

    } catch (error) {
      console.error('Error starting recording:', error);
      Alert.alert('خطأ', 'فشل في بدء التسجيل الصوتي');
      throw error;
    }
  }

  /**
   * Stop voice recording and get URI
   */
  async stopRecording(): Promise<string | null> {
    try {
      if (!this.recording || !this.isRecording) {
        return null;
      }

      await this.recording.stopAndUnloadAsync();
      this.recordingUri = this.recording.getURI();
      this.isRecording = false;
      this.recording = null;

      return this.recordingUri;
    } catch (error) {
      console.error('Error stopping recording:', error);
      Alert.alert('خطأ', 'فشل في إيقاف التسجيل الصوتي');
      throw error;
    }
  }

  /**
   * Transcribe audio using AI-powered speech recognition
   */
  async transcribeAudio(
    audioUri: string, 
    options: VoiceRecognitionOptions = {}
  ): Promise<VoiceRecognitionResult> {
    try {
      // Read audio file
      const audioBase64 = await FileSystem.readAsStringAsync(audioUri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Use OpenRouter for transcription with Arabic support
      const transcriptionPrompt = `
تحويل الصوت إلى نص باللغة العربية. يرجى:
1. تحويل الكلام المسجل إلى نص دقيق
2. تحديد اللهجة (فصحى، سورية، مختلطة)
3. استخراج المعلومات المهنية إن وجدت (مهارات، خدمات، خبرة، أسعار)
4. تقدير مستوى الثقة في التحويل

الصوت المرفق: [AUDIO_DATA]

يرجى الرد بتنسيق JSON:
{
  "transcript": "النص المحول",
  "confidence": 0.95,
  "language": "ar",
  "dialect": "syrian",
  "extractedData": {
    "skills": ["مهارة1", "مهارة2"],
    "services": ["خدمة1", "خدمة2"],
    "experience": "وصف الخبرة",
    "pricing": 50
  }
}
`;

      const response = await openRouterService.generateResponse(transcriptionPrompt, {
        model: 'openai/gpt-4-turbo-preview',
        temperature: 0.3,
        max_tokens: 1000,
      });

      // Parse AI response
      const result = this.parseTranscriptionResponse(response);
      
      // Apply dialect detection if enabled
      if (options.enableDialectDetection) {
        result.dialect = await this.detectArabicDialect(result.transcript);
      }

      return result;

    } catch (error) {
      console.error('Error transcribing audio:', error);
      Alert.alert('خطأ', 'فشل في تحويل الصوت إلى نص');
      throw error;
    }
  }

  /**
   * Record and transcribe in one step
   */
  async recordAndTranscribe(options: VoiceRecognitionOptions = {}): Promise<VoiceRecognitionResult> {
    try {
      // Start recording
      await this.startRecording(options);
      
      // Wait for user to stop or auto-stop
      return new Promise((resolve, reject) => {
        const checkRecording = setInterval(async () => {
          if (!this.isRecording) {
            clearInterval(checkRecording);
            
            try {
              const audioUri = await this.stopRecording();
              if (audioUri) {
                const result = await this.transcribeAudio(audioUri, options);
                resolve(result);
              } else {
                reject(new Error('No audio recorded'));
              }
            } catch (error) {
              reject(error);
            }
          }
        }, 100);
      });

    } catch (error) {
      console.error('Error in record and transcribe:', error);
      throw error;
    }
  }

  /**
   * Detect Arabic dialect from text
   */
  private async detectArabicDialect(text: string): Promise<'formal' | 'syrian' | 'mixed'> {
    const syrianIndicators = [
      'شو', 'كيفك', 'هيك', 'بدي', 'عم', 'لحتى', 'منيح', 'هلأ', 'شلون', 'وين'
    ];
    
    const formalIndicators = [
      'ماذا', 'كيف حالك', 'هكذا', 'أريد', 'أن', 'حتى', 'جيد', 'الآن', 'كيف', 'أين'
    ];

    const syrianCount = syrianIndicators.filter(word => text.includes(word)).length;
    const formalCount = formalIndicators.filter(word => text.includes(word)).length;

    if (syrianCount > formalCount) return 'syrian';
    if (formalCount > syrianCount) return 'formal';
    return 'mixed';
  }

  /**
   * Parse AI transcription response
   */
  private parseTranscriptionResponse(response: string): VoiceRecognitionResult {
    try {
      // Try to parse JSON response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          transcript: parsed.transcript || '',
          confidence: parsed.confidence || 0.8,
          language: parsed.language || 'ar',
          dialect: parsed.dialect || 'formal',
          extractedData: parsed.extractedData || {}
        };
      }

      // Fallback: treat entire response as transcript
      return {
        transcript: response,
        confidence: 0.7,
        language: 'ar',
        dialect: 'formal'
      };

    } catch (error) {
      console.error('Error parsing transcription response:', error);
      return {
        transcript: response,
        confidence: 0.5,
        language: 'ar',
        dialect: 'formal'
      };
    }
  }

  /**
   * Get recording status
   */
  getRecordingStatus(): { isRecording: boolean; duration?: number } {
    return {
      isRecording: this.isRecording,
      // duration can be added if needed
    };
  }

  /**
   * Process voice with Phase 3 AI integration
   */
  async processVoiceWithAI(
    sessionId: string,
    audioUri: string,
    dialect: string = 'general'
  ): Promise<VoiceProcessingResult> {
    try {
      console.log('🎤 Processing voice with Phase 3 AI:', { sessionId, dialect });

      // Use Phase 3 AI service for enhanced voice processing
      const result = await phase3AIService.processVoice(sessionId, audioUri, dialect);

      console.log('✅ Voice processed with AI:', {
        transcription: result.transcription.substring(0, 50) + '...',
        confidence: result.confidence,
        dialect: result.dialect
      });

      return result;
    } catch (error) {
      console.error('❌ Failed to process voice with AI:', error);
      throw error;
    }
  }

  /**
   * Record and process with Phase 3 AI in one step
   */
  async recordAndProcessWithAI(
    sessionId: string,
    options: VoiceRecognitionOptions = {}
  ): Promise<VoiceProcessingResult> {
    try {
      console.log('🎙️ Starting record and process with AI:', { sessionId });

      // Start recording
      await this.startRecording(options);

      // Wait for recording to complete
      return new Promise((resolve, reject) => {
        const checkRecording = setInterval(async () => {
          if (!this.isRecording) {
            clearInterval(checkRecording);

            try {
              const audioUri = await this.stopRecording();
              if (audioUri) {
                const dialect = options.enableDialectDetection ? 'syrian' : 'general';
                const result = await this.processVoiceWithAI(sessionId, audioUri, dialect);
                resolve(result);
              } else {
                reject(new Error('No audio recorded'));
              }
            } catch (error) {
              reject(error);
            }
          }
        }, 100);
      });
    } catch (error) {
      console.error('❌ Error in record and process with AI:', error);
      throw error;
    }
  }

  /**
   * Enhanced Syrian dialect detection with AI
   */
  async detectSyrianDialectWithAI(text: string): Promise<{
    dialect: 'damascus' | 'aleppo' | 'homs' | 'latakia' | 'general';
    confidence: number;
    culturalContext: string[];
  }> {
    try {
      const dialectPrompt = `
تحليل اللهجة السورية في النص التالي وتحديد المنطقة:

النص: "${text}"

يرجى تحديد:
1. اللهجة المحددة (دمشق، حلب، حمص، اللاذقية، عامة)
2. مستوى الثقة (0-1)
3. السياق الثقافي والكلمات المميزة

الرد بتنسيق JSON:
{
  "dialect": "damascus",
  "confidence": 0.85,
  "culturalContext": ["كلمة1", "كلمة2"]
}
`;

      const response = await openRouterService.generateResponse(dialectPrompt, {
        model: 'openai/gpt-4-turbo-preview',
        temperature: 0.3,
        max_tokens: 500,
      });

      // Parse AI response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          dialect: parsed.dialect || 'general',
          confidence: parsed.confidence || 0.7,
          culturalContext: parsed.culturalContext || []
        };
      }

      // Fallback to basic detection
      return {
        dialect: this.detectBasicSyrianDialect(text),
        confidence: 0.6,
        culturalContext: []
      };
    } catch (error) {
      console.error('Error detecting Syrian dialect with AI:', error);
      return {
        dialect: 'general',
        confidence: 0.5,
        culturalContext: []
      };
    }
  }

  /**
   * Basic Syrian dialect detection (fallback)
   */
  private detectBasicSyrianDialect(text: string): 'damascus' | 'aleppo' | 'homs' | 'latakia' | 'general' {
    const damascusWords = ['شو', 'كيفك', 'هيك', 'بدي', 'عم'];
    const aleppoWords = ['شلون', 'وين', 'هون', 'تمام'];
    const homsWords = ['أيش', 'كيف', 'هلأ'];
    const latakiaWords = ['شو', 'كيفك', 'منيح'];

    const damascusCount = damascusWords.filter(word => text.includes(word)).length;
    const aleppoCount = aleppoWords.filter(word => text.includes(word)).length;
    const homsCount = homsWords.filter(word => text.includes(word)).length;
    const latakiaCount = latakiaWords.filter(word => text.includes(word)).length;

    const max = Math.max(damascusCount, aleppoCount, homsCount, latakiaCount);

    if (max === 0) return 'general';
    if (max === damascusCount) return 'damascus';
    if (max === aleppoCount) return 'aleppo';
    if (max === homsCount) return 'homs';
    if (max === latakiaCount) return 'latakia';

    return 'general';
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    try {
      if (this.recording && this.isRecording) {
        await this.stopRecording();
      }

      if (this.recordingUri) {
        await FileSystem.deleteAsync(this.recordingUri, { idempotent: true });
        this.recordingUri = null;
      }
    } catch (error) {
      console.error('Error cleaning up voice recognition:', error);
    }
  }
}

// Export singleton instance
export const voiceRecognitionService = new VoiceRecognitionService();
