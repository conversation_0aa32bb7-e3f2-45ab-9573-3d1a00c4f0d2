const express = require('express');

console.log('Starting debug server...');

const app = express();
const PORT = 3001;

app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'Debug server is running' });
});

app.get('/api/v1/ai/test-connection', (req, res) => {
  res.json({ 
    success: true, 
    message: 'AI endpoint is accessible',
    timestamp: new Date().toISOString()
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Debug server running on port ${PORT}`);
  console.log(`🏥 Health: http://localhost:${PORT}/health`);
  console.log(`🤖 AI Test: http://localhost:${PORT}/api/v1/ai/test-connection`);
});
