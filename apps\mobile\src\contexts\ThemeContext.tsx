import React, { createContext, useContext, ReactNode } from 'react';
import { useAppStore } from '../store/appStore';

// Theme types
interface Colors {
  primary: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
  };
  secondary: string;
  background: string;
  surface: string;
  text: {
    primary: string;
    secondary: string;
    muted: string;
  };
  border: string;
  error: string;
  warning: string;
  success: string;
  info: string;
  white: string;
  black: string;
  glass: {
    background: string;
    border: string;
    shadow: string;
  };
  // Backward compatibility
  primaryDark: string;
  primaryLight: string;
  onPrimary: string;
  textSecondary: string;
}

interface Theme {
  colors: Colors;
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    xxl: number;
  };
  borderRadius: {
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  typography: {
    h1: number;
    h2: number;
    h3: number;
    h4: number;
    body: number;
    caption: number;
  };
}

// Gold theme (Premium)
const goldTheme: Theme = {
  colors: {
    primary: {
      50: '#FFFBEB',
      100: '#FEF3C7',
      200: '#FDE68A',
      300: '#FCD34D',
      400: '#FBBF24',
      500: '#F59E0B', // Main gold
      600: '#D97706',
      700: '#B45309',
      800: '#92400E',
      900: '#78350F',
    },
    secondary: '#10B981',
    background: '#0F0F0F',
    surface: '#1A1A1A',
    text: {
      primary: '#F9FAFB',
      secondary: '#D1D5DB',
      muted: '#9CA3AF',
    },
    border: '#374151',
    error: '#F87171',
    warning: '#FBBF24',
    success: '#34D399',
    info: '#60A5FA',
    white: '#FFFFFF',
    black: '#000000',
    glass: {
      background: 'rgba(255, 215, 0, 0.12)',
      border: 'rgba(255, 215, 0, 0.25)',
      shadow: '#FFD700',
    },
    // Backward compatibility
    primaryDark: '#D97706',
    primaryLight: '#FEF3C7',
    onPrimary: '#FFFFFF',
    textSecondary: '#D1D5DB',
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
  },
  typography: {
    h1: 32,
    h2: 28,
    h3: 24,
    h4: 20,
    body: 16,
    caption: 14,
  },
};

// Purple theme (Alternative)
const purpleTheme: Theme = {
  ...goldTheme,
  colors: {
    primary: {
      50: '#FAF5FF',
      100: '#F3E8FF',
      200: '#E9D5FF',
      300: '#D8B4FE',
      400: '#C084FC',
      500: '#A855F7', // Main purple
      600: '#9333EA',
      700: '#7C3AED',
      800: '#6B21A8',
      900: '#581C87',
    },
    secondary: '#10B981',
    background: '#0F0F0F',
    surface: '#1A1A1A',
    text: {
      primary: '#F9FAFB',
      secondary: '#D1D5DB',
      muted: '#9CA3AF',
    },
    border: '#374151',
    error: '#F87171',
    warning: '#FBBF24',
    success: '#34D399',
    info: '#60A5FA',
    white: '#FFFFFF',
    black: '#000000',
    glass: {
      background: 'rgba(217, 70, 239, 0.12)',
      border: 'rgba(217, 70, 239, 0.25)',
      shadow: '#d946ef',
    },
    // Backward compatibility
    primaryDark: '#9333EA',
    primaryLight: '#F3E8FF',
    onPrimary: '#FFFFFF',
    textSecondary: '#D1D5DB',
  },
};

// Context
interface ThemeContextType {
  currentTheme: Theme;
  themeType: 'gold' | 'purple';
  setThemeType: (type: 'gold' | 'purple') => void;
  // Backward compatibility
  theme: Theme;
  colors: Colors;
  isDark: boolean;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Provider
interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { theme: themeMode, setTheme } = useAppStore();

  // Support both old (light/dark) and new (gold/purple) theme modes
  const isDark = themeMode === 'dark';
  const themeType = (themeMode === 'purple' || themeMode === 'gold')
    ? themeMode as 'gold' | 'purple'
    : 'gold'; // Default to gold

  const currentTheme = themeType === 'purple' ? purpleTheme : goldTheme;

  const setThemeType = (type: 'gold' | 'purple') => {
    setTheme(type);
  };

  const toggleTheme = () => {
    // Toggle between gold and purple for new system
    setTheme(themeType === 'gold' ? 'purple' : 'gold');
  };

  const value: ThemeContextType = {
    currentTheme,
    themeType,
    setThemeType,
    // Backward compatibility
    theme: currentTheme,
    colors: currentTheme.colors,
    isDark: true, // Always dark mode for glass morphism
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

// Hook
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
