console.log('Testing app initialization...');

try {
  console.log('Loading config...');
  const { config } = require('./dist/config/index.js');
  console.log('✅ Config loaded');
  
  console.log('Loading App class...');
  const App = require('./dist/app.js').default;
  console.log('✅ App class loaded');
  
  console.log('Creating app instance...');
  const app = new App();
  console.log('✅ App instance created');
  
  console.log('Initializing app...');
  app.initialize().then(() => {
    console.log('✅ App initialized successfully');
    
    console.log('Starting server...');
    const server = app.app.listen(3001, () => {
      console.log('🚀 Server running on port 3001');
      console.log('🏥 Health: http://localhost:3001/health');
      
      // Test the health endpoint
      setTimeout(() => {
        console.log('Testing health endpoint...');
        fetch('http://localhost:3001/health')
          .then(res => res.json())
          .then(data => {
            console.log('✅ Health check response:', data);
            server.close();
          })
          .catch(err => {
            console.error('❌ Health check failed:', err);
            server.close();
          });
      }, 1000);
    });
    
  }).catch(error => {
    console.error('❌ App initialization failed:');
    console.error('Error message:', error.message);
    console.error('Error stack:', error.stack);
    console.error('Error details:', error);
  });
  
} catch (error) {
  console.error('❌ Error during setup:');
  console.error('Error message:', error.message);
  console.error('Error stack:', error.stack);
}
