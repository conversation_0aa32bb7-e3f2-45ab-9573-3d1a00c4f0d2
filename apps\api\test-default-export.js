console.log('Testing default export...');

try {
  const security = require('./dist/middleware/security.js');
  console.log('Default export:', security.default);
  console.log('Default export keys:', Object.keys(security.default || {}));
  
  if (security.default) {
    console.log('\nDefault export properties:');
    Object.keys(security.default).forEach(key => {
      console.log(`${key}: ${typeof security.default[key]}`);
    });
  }
  
} catch (error) {
  console.error('❌ Error:', error);
}
