"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.cacheHelpers = exports.sessionHelpers = exports.redis = void 0;
const redis_1 = require("redis");
const config_1 = require("../config");
const logger_1 = require("./logger");
class RedisClient {
    client;
    isConnected;
    constructor() {
        this.client = (0, redis_1.createClient)({
            url: config_1.redisConfig.url,
            socket: {
                reconnectStrategy: (retries) => {
                    if (retries > 10) {
                        logger_1.logger.error('Redis: Maximum reconnection attempts reached');
                        return new Error('Redis: Maximum reconnection attempts reached');
                    }
                    return Math.min(retries * 100, 3000);
                },
            },
        });
        this.isConnected = false;
        this.setupEventHandlers();
    }
    setupEventHandlers() {
        this.client.on('connect', () => {
            logger_1.logger.info('Redis: Connecting...');
        });
        this.client.on('ready', () => {
            this.isConnected = true;
            logger_1.logger.info('Redis: Connected and ready');
        });
        this.client.on('error', (error) => {
            logger_1.logger.error('Redis: Connection error', { error: error.message });
            this.isConnected = false;
        });
        this.client.on('end', () => {
            this.isConnected = false;
            logger_1.logger.info('Redis: Connection ended');
        });
        this.client.on('reconnecting', () => {
            logger_1.logger.info('Redis: Reconnecting...');
        });
    }
    async connect() {
        if (this.isConnected) {
            logger_1.logger.info('Redis: Already connected');
            return;
        }
        try {
            logger_1.logger.info('Redis: Connecting...');
            await Promise.race([
                this.client.connect(),
                new Promise((_, reject) => setTimeout(() => reject(new Error('Redis connection timed out after 10 seconds')), 10000)),
            ]);
        }
        catch (error) {
            if (error instanceof Error) {
                logger_1.logger.error('Redis: Failed to connect', { error: error.message });
            }
            else {
                logger_1.logger.error('Redis: Failed to connect with an unknown error', { error });
            }
        }
    }
    async disconnect() {
        if (!this.isConnected) {
            return;
        }
        try {
            await this.client.disconnect();
        }
        catch (error) {
            logger_1.logger.error('Redis: Failed to disconnect', { error });
        }
    }
    isReady() {
        return this.isConnected;
    }
    async get(key) {
        if (!this.isReady())
            return null;
        try {
            return await this.client.get(key);
        }
        catch (error) {
            logger_1.logger.error('Redis: GET operation failed', { key, error });
            return null;
        }
    }
    async set(key, value, ttl) {
        if (!this.isReady())
            return false;
        try {
            if (ttl) {
                await this.client.setEx(key, ttl, value);
            }
            else {
                await this.client.set(key, value);
            }
            return true;
        }
        catch (error) {
            logger_1.logger.error('Redis: SET operation failed', { key, error });
            return false;
        }
    }
    async del(key) {
        if (!this.isReady())
            return false;
        try {
            const result = await this.client.del(key);
            return result > 0;
        }
        catch (error) {
            logger_1.logger.error('Redis: DEL operation failed', { key, error });
            return false;
        }
    }
    async exists(key) {
        try {
            const result = await this.client.exists(key);
            return result > 0;
        }
        catch (error) {
            logger_1.logger.error('Redis: EXISTS operation failed', { key, error });
            return false;
        }
    }
    async expire(key, ttl) {
        try {
            return await this.client.expire(key, ttl);
        }
        catch (error) {
            logger_1.logger.error('Redis: EXPIRE operation failed', { key, ttl, error });
            return false;
        }
    }
    // JSON operations
    async getJSON(key) {
        try {
            const value = await this.get(key);
            return value ? JSON.parse(value) : null;
        }
        catch (error) {
            logger_1.logger.error('Redis: JSON GET operation failed', { key, error });
            return null;
        }
    }
    async setJSON(key, value, ttl) {
        try {
            return await this.set(key, JSON.stringify(value), ttl);
        }
        catch (error) {
            logger_1.logger.error('Redis: JSON SET operation failed', { key, error });
            return false;
        }
    }
    // Hash operations
    async hGet(key, field) {
        try {
            const result = await this.client.hGet(key, field);
            return result || null;
        }
        catch (error) {
            logger_1.logger.error('Redis: HGET operation failed', { key, field, error });
            return null;
        }
    }
    async hSet(key, field, value) {
        try {
            await this.client.hSet(key, field, value);
            return true;
        }
        catch (error) {
            logger_1.logger.error('Redis: HSET operation failed', { key, field, error });
            return false;
        }
    }
    async hGetAll(key) {
        try {
            return await this.client.hGetAll(key);
        }
        catch (error) {
            logger_1.logger.error('Redis: HGETALL operation failed', { key, error });
            return null;
        }
    }
    async hDel(key, field) {
        try {
            const result = await this.client.hDel(key, field);
            return result > 0;
        }
        catch (error) {
            logger_1.logger.error('Redis: HDEL operation failed', { key, field, error });
            return false;
        }
    }
    // List operations
    async lPush(key, value) {
        try {
            await this.client.lPush(key, value);
            return true;
        }
        catch (error) {
            logger_1.logger.error('Redis: LPUSH operation failed', { key, error });
            return false;
        }
    }
    async rPop(key) {
        try {
            return await this.client.rPop(key);
        }
        catch (error) {
            logger_1.logger.error('Redis: RPOP operation failed', { key, error });
            return null;
        }
    }
    // Set operations
    async sAdd(key, member) {
        try {
            const result = await this.client.sAdd(key, member);
            return result > 0;
        }
        catch (error) {
            logger_1.logger.error('Redis: SADD operation failed', { key, member, error });
            return false;
        }
    }
    async sIsMember(key, member) {
        try {
            return await this.client.sIsMember(key, member);
        }
        catch (error) {
            logger_1.logger.error('Redis: SISMEMBER operation failed', { key, member, error });
            return false;
        }
    }
    async sRem(key, member) {
        try {
            const result = await this.client.sRem(key, member);
            return result > 0;
        }
        catch (error) {
            logger_1.logger.error('Redis: SREM operation failed', { key, member, error });
            return false;
        }
    }
    // Utility methods
    async flushAll() {
        try {
            await this.client.flushAll();
            return true;
        }
        catch (error) {
            logger_1.logger.error('Redis: FLUSHALL operation failed', { error });
            return false;
        }
    }
    async ping() {
        try {
            const result = await this.client.ping();
            return result === 'PONG';
        }
        catch (error) {
            logger_1.logger.error('Redis: PING operation failed', { error });
            return false;
        }
    }
    // Get the raw client for advanced operations
    getClient() {
        return this.client;
    }
}
// Create singleton instance
exports.redis = new RedisClient();
// Session management helpers
exports.sessionHelpers = {
    async setSession(sessionId, userId, data, ttl = 86400) {
        const sessionData = {
            userId,
            data,
            createdAt: new Date().toISOString(),
        };
        return await exports.redis.setJSON(`session:${sessionId}`, sessionData, ttl);
    },
    async getSession(sessionId) {
        return await exports.redis.getJSON(`session:${sessionId}`);
    },
    async deleteSession(sessionId) {
        return await exports.redis.del(`session:${sessionId}`);
    },
    async refreshSession(sessionId, ttl = 86400) {
        return await exports.redis.expire(`session:${sessionId}`, ttl);
    },
};
// Cache helpers
exports.cacheHelpers = {
    async cacheUserData(userId, data, ttl = 3600) {
        return await exports.redis.setJSON(`user:${userId}`, data, ttl);
    },
    async getUserData(userId) {
        return await exports.redis.getJSON(`user:${userId}`);
    },
    async invalidateUserCache(userId) {
        return await exports.redis.del(`user:${userId}`);
    },
    async cacheServiceData(serviceId, data, ttl = 1800) {
        return await exports.redis.setJSON(`service:${serviceId}`, data, ttl);
    },
    async getServiceData(serviceId) {
        return await exports.redis.getJSON(`service:${serviceId}`);
    },
    async invalidateServiceCache(serviceId) {
        return await exports.redis.del(`service:${serviceId}`);
    },
};
exports.default = exports.redis;
//# sourceMappingURL=redis.js.map