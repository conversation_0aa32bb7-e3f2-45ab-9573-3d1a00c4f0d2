import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

// Store
import { useAuthStore } from '../store/authStore';

// Navigators
import AuthNavigator from './AuthNavigator';
import MainNavigator from './MainNavigator';

// Screens
import SplashScreen from '../screens/SplashScreen';
import OnboardingScreen from '../screens/OnboardingScreen';
import AIOnboardingScreen from '../screens/onboarding/AIOnboardingScreen';

// Types
import { RootStackParamList } from '../types/navigation';

const Stack = createStackNavigator<RootStackParamList>();

const RootNavigator: React.FC = () => {
  const { isAuthenticated, isLoading, hasCompletedOnboarding } = useAuthStore();

  if (isLoading) {
    return <SplashScreen />;
  }

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        gestureDirection: 'horizontal-inverted', // RTL gesture support
      }}
    >
      {!hasCompletedOnboarding ? (
        <>
          <Stack.Screen
            name="Onboarding"
            component={OnboardingScreen}
            options={{
              gestureEnabled: false,
            }}
          />
          <Stack.Screen
            name="AIOnboarding"
            component={AIOnboardingScreen}
            options={{
              gestureEnabled: false,
              headerShown: false,
            }}
          />
        </>
      ) : !isAuthenticated ? (
        <Stack.Screen
          name="Auth"
          component={AuthNavigator}
          options={{
            animationTypeForReplace: 'pop',
          }}
        />
      ) : (
        <Stack.Screen
          name="Main"
          component={MainNavigator}
          options={{
            animationTypeForReplace: 'push',
          }}
        />
      )}
    </Stack.Navigator>
  );
};

export default RootNavigator;
