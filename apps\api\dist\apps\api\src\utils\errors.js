"use strict";
/**
 * Comprehensive Error Handling Utility for Freela Syria API
 * Provides structured error management with Arabic/English support
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.isOperationalError = exports.createError = exports.AppError = exports.errorMessages = exports.ErrorSeverity = exports.ErrorType = exports.HTTP_STATUS = void 0;
// HTTP Status Code Mapping
exports.HTTP_STATUS = {
    OK: 200,
    CREATED: 201,
    NO_CONTENT: 204,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    CONFLICT: 409,
    UNPROCESSABLE_ENTITY: 422,
    TOO_MANY_REQUESTS: 429,
    INTERNAL_SERVER_ERROR: 500,
    BAD_GATEWAY: 502,
    SERVICE_UNAVAILABLE: 503,
};
// Error Types for categorization
var ErrorType;
(function (ErrorType) {
    ErrorType["VALIDATION"] = "VALIDATION";
    ErrorType["AUTHENTICATION"] = "AUTHENTICATION";
    ErrorType["AUTHORIZATION"] = "AUTHORIZATION";
    ErrorType["NOT_FOUND"] = "NOT_FOUND";
    ErrorType["CONFLICT"] = "CONFLICT";
    ErrorType["RATE_LIMIT"] = "RATE_LIMIT";
    ErrorType["EXTERNAL_SERVICE"] = "EXTERNAL_SERVICE";
    ErrorType["DATABASE"] = "DATABASE";
    ErrorType["FILE_UPLOAD"] = "FILE_UPLOAD";
    ErrorType["AI_SERVICE"] = "AI_SERVICE";
    ErrorType["WEBSOCKET"] = "WEBSOCKET";
    ErrorType["INTERNAL"] = "INTERNAL";
})(ErrorType || (exports.ErrorType = ErrorType = {}));
// Error severity levels
var ErrorSeverity;
(function (ErrorSeverity) {
    ErrorSeverity["LOW"] = "LOW";
    ErrorSeverity["MEDIUM"] = "MEDIUM";
    ErrorSeverity["HIGH"] = "HIGH";
    ErrorSeverity["CRITICAL"] = "CRITICAL";
})(ErrorSeverity || (exports.ErrorSeverity = ErrorSeverity = {}));
// Bilingual error messages (Arabic/English)
exports.errorMessages = {
    // Authentication errors
    INVALID_CREDENTIALS: {
        en: 'Invalid email or password',
        ar: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
    },
    TOKEN_EXPIRED: {
        en: 'Authentication token has expired',
        ar: 'انتهت صلاحية رمز المصادقة',
    },
    TOKEN_INVALID: {
        en: 'Invalid authentication token',
        ar: 'رمز المصادقة غير صالح',
    },
    ACCESS_DENIED: {
        en: 'Access denied. Insufficient permissions',
        ar: 'تم رفض الوصول. صلاحيات غير كافية',
    },
    // Validation errors
    VALIDATION_FAILED: {
        en: 'Validation failed',
        ar: 'فشل في التحقق من صحة البيانات',
    },
    REQUIRED_FIELD: {
        en: 'This field is required',
        ar: 'هذا الحقل مطلوب',
    },
    INVALID_EMAIL: {
        en: 'Invalid email format',
        ar: 'تنسيق البريد الإلكتروني غير صالح',
    },
    INVALID_PHONE: {
        en: 'Invalid phone number format',
        ar: 'تنسيق رقم الهاتف غير صالح',
    },
    // Resource errors
    USER_NOT_FOUND: {
        en: 'User not found',
        ar: 'المستخدم غير موجود',
    },
    EXPERT_NOT_FOUND: {
        en: 'Expert not found',
        ar: 'الخبير غير موجود',
    },
    SERVICE_NOT_FOUND: {
        en: 'Service not found',
        ar: 'الخدمة غير موجودة',
    },
    BOOKING_NOT_FOUND: {
        en: 'Booking not found',
        ar: 'الحجز غير موجود',
    },
    // Conflict errors
    EMAIL_EXISTS: {
        en: 'Email address already exists',
        ar: 'عنوان البريد الإلكتروني موجود بالفعل',
    },
    PHONE_EXISTS: {
        en: 'Phone number already exists',
        ar: 'رقم الهاتف موجود بالفعل',
    },
    // AI Service errors
    AI_SERVICE_UNAVAILABLE: {
        en: 'AI service is temporarily unavailable',
        ar: 'خدمة الذكاء الاصطناعي غير متاحة مؤقتاً',
    },
    AI_QUOTA_EXCEEDED: {
        en: 'AI service quota exceeded',
        ar: 'تم تجاوز حصة خدمة الذكاء الاصطناعي',
    },
    INVALID_AI_REQUEST: {
        en: 'Invalid AI service request',
        ar: 'طلب خدمة الذكاء الاصطناعي غير صالح',
    },
    // File upload errors
    FILE_TOO_LARGE: {
        en: 'File size exceeds maximum limit',
        ar: 'حجم الملف يتجاوز الحد الأقصى',
    },
    INVALID_FILE_TYPE: {
        en: 'Invalid file type',
        ar: 'نوع الملف غير صالح',
    },
    // Rate limiting
    RATE_LIMIT_EXCEEDED: {
        en: 'Too many requests. Please try again later',
        ar: 'طلبات كثيرة جداً. يرجى المحاولة مرة أخرى لاحقاً',
    },
    // Generic errors
    INTERNAL_ERROR: {
        en: 'Internal server error',
        ar: 'خطأ داخلي في الخادم',
    },
    SERVICE_UNAVAILABLE: {
        en: 'Service temporarily unavailable',
        ar: 'الخدمة غير متاحة مؤقتاً',
    },
    DATABASE_ERROR: {
        en: 'Database operation failed',
        ar: 'فشلت عملية قاعدة البيانات',
    },
};
// Custom Application Error Class
class AppError extends Error {
    statusCode;
    type;
    severity;
    isOperational;
    timestamp;
    messageAr;
    details;
    path;
    method;
    userId;
    requestId;
    constructor(message, statusCode = exports.HTTP_STATUS.INTERNAL_SERVER_ERROR, type = ErrorType.INTERNAL, severity = ErrorSeverity.MEDIUM, isOperational = true, messageAr, details) {
        super(message);
        this.name = this.constructor.name;
        this.statusCode = statusCode;
        this.type = type;
        this.severity = severity;
        this.isOperational = isOperational;
        this.timestamp = new Date().toISOString();
        this.messageAr = messageAr;
        this.details = details;
        // Capture stack trace
        Error.captureStackTrace(this, this.constructor);
    }
    // Add request context to error
    addContext(req) {
        this.path = req.path;
        this.method = req.method;
        this.userId = req.user?.id;
        this.requestId = req.headers['x-request-id'];
        return this;
    }
    // Convert to JSON for API responses
    toJSON() {
        return {
            success: false,
            error: {
                message: this.message,
                messageAr: this.messageAr,
                type: this.type,
                statusCode: this.statusCode,
                timestamp: this.timestamp,
                ...(this.details && { details: this.details }),
                ...(this.path && { path: this.path }),
                ...(this.method && { method: this.method }),
                ...(this.requestId && { requestId: this.requestId }),
            },
        };
    }
}
exports.AppError = AppError;
// Error creation utilities
exports.createError = {
    // Authentication errors
    invalidCredentials: (messageAr) => new AppError(exports.errorMessages.INVALID_CREDENTIALS.en, exports.HTTP_STATUS.UNAUTHORIZED, ErrorType.AUTHENTICATION, ErrorSeverity.MEDIUM, true, messageAr || exports.errorMessages.INVALID_CREDENTIALS.ar),
    tokenExpired: (messageAr) => new AppError(exports.errorMessages.TOKEN_EXPIRED.en, exports.HTTP_STATUS.UNAUTHORIZED, ErrorType.AUTHENTICATION, ErrorSeverity.MEDIUM, true, messageAr || exports.errorMessages.TOKEN_EXPIRED.ar),
    tokenInvalid: (messageAr) => new AppError(exports.errorMessages.TOKEN_INVALID.en, exports.HTTP_STATUS.UNAUTHORIZED, ErrorType.AUTHENTICATION, ErrorSeverity.MEDIUM, true, messageAr || exports.errorMessages.TOKEN_INVALID.ar),
    accessDenied: (messageAr) => new AppError(exports.errorMessages.ACCESS_DENIED.en, exports.HTTP_STATUS.FORBIDDEN, ErrorType.AUTHORIZATION, ErrorSeverity.HIGH, true, messageAr || exports.errorMessages.ACCESS_DENIED.ar),
    // Validation errors
    validationFailed: (details, messageAr) => new AppError(exports.errorMessages.VALIDATION_FAILED.en, exports.HTTP_STATUS.BAD_REQUEST, ErrorType.VALIDATION, ErrorSeverity.LOW, true, messageAr || exports.errorMessages.VALIDATION_FAILED.ar, details),
    // Resource not found errors
    userNotFound: (userId, messageAr) => new AppError(exports.errorMessages.USER_NOT_FOUND.en, exports.HTTP_STATUS.NOT_FOUND, ErrorType.NOT_FOUND, ErrorSeverity.LOW, true, messageAr || exports.errorMessages.USER_NOT_FOUND.ar, userId ? { userId } : undefined),
    expertNotFound: (expertId, messageAr) => new AppError(exports.errorMessages.EXPERT_NOT_FOUND.en, exports.HTTP_STATUS.NOT_FOUND, ErrorType.NOT_FOUND, ErrorSeverity.LOW, true, messageAr || exports.errorMessages.EXPERT_NOT_FOUND.ar, expertId ? { expertId } : undefined),
    serviceNotFound: (serviceId, messageAr) => new AppError(exports.errorMessages.SERVICE_NOT_FOUND.en, exports.HTTP_STATUS.NOT_FOUND, ErrorType.NOT_FOUND, ErrorSeverity.LOW, true, messageAr || exports.errorMessages.SERVICE_NOT_FOUND.ar, serviceId ? { serviceId } : undefined),
    // Conflict errors
    emailExists: (email, messageAr) => new AppError(exports.errorMessages.EMAIL_EXISTS.en, exports.HTTP_STATUS.CONFLICT, ErrorType.CONFLICT, ErrorSeverity.LOW, true, messageAr || exports.errorMessages.EMAIL_EXISTS.ar, email ? { email } : undefined),
    // AI Service errors
    aiServiceUnavailable: (details, messageAr) => new AppError(exports.errorMessages.AI_SERVICE_UNAVAILABLE.en, exports.HTTP_STATUS.SERVICE_UNAVAILABLE, ErrorType.AI_SERVICE, ErrorSeverity.HIGH, true, messageAr || exports.errorMessages.AI_SERVICE_UNAVAILABLE.ar, details),
    aiQuotaExceeded: (details, messageAr) => new AppError(exports.errorMessages.AI_QUOTA_EXCEEDED.en, exports.HTTP_STATUS.TOO_MANY_REQUESTS, ErrorType.AI_SERVICE, ErrorSeverity.MEDIUM, true, messageAr || exports.errorMessages.AI_QUOTA_EXCEEDED.ar, details),
    invalidAiRequest: (details, messageAr) => new AppError(exports.errorMessages.INVALID_AI_REQUEST.en, exports.HTTP_STATUS.BAD_REQUEST, ErrorType.AI_SERVICE, ErrorSeverity.LOW, true, messageAr || exports.errorMessages.INVALID_AI_REQUEST.ar, details),
    // Rate limiting
    rateLimitExceeded: (details, messageAr) => new AppError(exports.errorMessages.RATE_LIMIT_EXCEEDED.en, exports.HTTP_STATUS.TOO_MANY_REQUESTS, ErrorType.RATE_LIMIT, ErrorSeverity.MEDIUM, true, messageAr || exports.errorMessages.RATE_LIMIT_EXCEEDED.ar, details),
    // Generic errors
    internalError: (details, messageAr) => new AppError(exports.errorMessages.INTERNAL_ERROR.en, exports.HTTP_STATUS.INTERNAL_SERVER_ERROR, ErrorType.INTERNAL, ErrorSeverity.CRITICAL, false, messageAr || exports.errorMessages.INTERNAL_ERROR.ar, details),
    databaseError: (details, messageAr) => new AppError(exports.errorMessages.DATABASE_ERROR.en, exports.HTTP_STATUS.INTERNAL_SERVER_ERROR, ErrorType.DATABASE, ErrorSeverity.HIGH, true, messageAr || exports.errorMessages.DATABASE_ERROR.ar, details),
    // Additional error creators for missing methods
    notFound: (message, messageAr, details) => new AppError(message || 'Resource not found', exports.HTTP_STATUS.NOT_FOUND, ErrorType.NOT_FOUND, ErrorSeverity.LOW, true, messageAr || 'المورد غير موجود', details),
    forbidden: (message, messageAr, details) => new AppError(message || 'Access forbidden', exports.HTTP_STATUS.FORBIDDEN, ErrorType.AUTHORIZATION, ErrorSeverity.MEDIUM, true, messageAr || 'الوصول محظور', details),
    badRequest: (message, messageAr, details) => new AppError(message || 'Bad request', exports.HTTP_STATUS.BAD_REQUEST, ErrorType.VALIDATION, ErrorSeverity.LOW, true, messageAr || 'طلب غير صالح', details),
    unauthorized: (message, messageAr, details) => new AppError(message || 'Unauthorized access', exports.HTTP_STATUS.UNAUTHORIZED, ErrorType.AUTHENTICATION, ErrorSeverity.MEDIUM, true, messageAr || 'وصول غير مصرح به', details),
    tooManyRequests: (message, messageAr, details) => new AppError(message || 'Too many requests', exports.HTTP_STATUS.TOO_MANY_REQUESTS, ErrorType.RATE_LIMIT, ErrorSeverity.MEDIUM, true, messageAr || 'طلبات كثيرة جداً', details),
    // Alias for backward compatibility
    internalServerError: (message, messageAr, details) => new AppError(message || exports.errorMessages.INTERNAL_ERROR.en, exports.HTTP_STATUS.INTERNAL_SERVER_ERROR, ErrorType.INTERNAL, ErrorSeverity.CRITICAL, false, messageAr || exports.errorMessages.INTERNAL_ERROR.ar, details),
    // Custom error creator
    custom: (message, statusCode, type, severity = ErrorSeverity.MEDIUM, messageAr, details) => new AppError(message, statusCode, type, severity, true, messageAr, details),
};
// Utility function to check if error is operational
const isOperationalError = (error) => {
    if (error instanceof AppError) {
        return error.isOperational;
    }
    return false;
};
exports.isOperationalError = isOperationalError;
//# sourceMappingURL=errors.js.map