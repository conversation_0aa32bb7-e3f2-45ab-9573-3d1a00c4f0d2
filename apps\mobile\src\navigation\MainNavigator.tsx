import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Store
import { useAuthStore } from '../store/authStore';

// Screens
import HomeScreen from '../screens/home/<USER>';
import SearchScreen from '../screens/search/SearchScreen';
import BookingsScreen from '../screens/bookings/BookingsScreen';
import ChatScreen from '../screens/chat/ChatScreen';
import ProfileScreen from '../screens/profile/ProfileScreen';

// AI Screens
import AIChatScreen from '../screens/chat/AIChatScreen';
import EnhancedAIChatScreen from '../screens/chat/EnhancedAIChatScreen';

// Expert Screens
import ExpertDashboardScreen from '../screens/expert/ExpertDashboardScreen';
import ServicesScreen from '../screens/expert/ServicesScreen';
import EarningsScreen from '../screens/expert/EarningsScreen';

// Types
import { ClientTabParamList, ExpertTabParamList, MainStackParamList } from '../types/navigation';

// Theme
import { useTheme } from '../contexts/ThemeContext';

const ClientTab = createBottomTabNavigator<ClientTabParamList>();
const ExpertTab = createBottomTabNavigator<ExpertTabParamList>();
const Stack = createStackNavigator<MainStackParamList>();

const ClientTabs: React.FC = () => {
  const { colors } = useTheme();

  return (
    <ClientTab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Home':
              iconName = 'home';
              break;
            case 'Search':
              iconName = 'search';
              break;
            case 'Bookings':
              iconName = 'bookmark';
              break;
            case 'Chat':
              iconName = 'chat';
              break;
            case 'Profile':
              iconName = 'person';
              break;
            default:
              iconName = 'help';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopColor: colors.border,
          paddingBottom: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontFamily: 'Cairo-Regular',
        },
      })}
    >
      <ClientTab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          tabBarLabel: 'الرئيسية',
        }}
      />
      <ClientTab.Screen
        name="Search"
        component={SearchScreen}
        options={{
          tabBarLabel: 'البحث',
        }}
      />
      <ClientTab.Screen
        name="Bookings"
        component={BookingsScreen}
        options={{
          tabBarLabel: 'الحجوزات',
        }}
      />
      <ClientTab.Screen
        name="Chat"
        component={ChatScreen}
        options={{
          tabBarLabel: 'المحادثات',
        }}
      />
      <ClientTab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          tabBarLabel: 'الملف الشخصي',
        }}
      />
    </ClientTab.Navigator>
  );
};

const ExpertTabs: React.FC = () => {
  const { colors } = useTheme();

  return (
    <ExpertTab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'dashboard';
              break;
            case 'Services':
              iconName = 'work';
              break;
            case 'Bookings':
              iconName = 'bookmark';
              break;
            case 'Chat':
              iconName = 'chat';
              break;
            case 'Earnings':
              iconName = 'attach-money';
              break;
            default:
              iconName = 'help';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopColor: colors.border,
          paddingBottom: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontFamily: 'Cairo-Regular',
        },
      })}
    >
      <ExpertTab.Screen
        name="Dashboard"
        component={ExpertDashboardScreen}
        options={{
          tabBarLabel: 'لوحة التحكم',
        }}
      />
      <ExpertTab.Screen
        name="Services"
        component={ServicesScreen}
        options={{
          tabBarLabel: 'خدماتي',
        }}
      />
      <ExpertTab.Screen
        name="Bookings"
        component={BookingsScreen}
        options={{
          tabBarLabel: 'الطلبات',
        }}
      />
      <ExpertTab.Screen
        name="Chat"
        component={ChatScreen}
        options={{
          tabBarLabel: 'المحادثات',
        }}
      />
      <ExpertTab.Screen
        name="Earnings"
        component={EarningsScreen}
        options={{
          tabBarLabel: 'الأرباح',
        }}
      />
    </ExpertTab.Navigator>
  );
};

const MainNavigator: React.FC = () => {
  const { user } = useAuthStore();
  const isExpert = user?.role === 'EXPERT';

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: true,
        gestureDirection: 'horizontal-inverted', // RTL support
      }}
    >
      <Stack.Screen
        name="Tabs"
        component={isExpert ? ExpertTabs : ClientTabs}
      />

      {/* AI Chat Screens */}
      <Stack.Screen
        name="AIChat"
        component={AIChatScreen}
        options={{
          headerShown: true,
          title: 'المساعد الذكي',
          headerTitleStyle: {
            fontFamily: 'Cairo-SemiBold',
          },
        }}
      />

      <Stack.Screen
        name="EnhancedAIChat"
        component={EnhancedAIChatScreen}
        options={{
          headerShown: true,
          title: 'المساعد الذكي المتطور',
          headerTitleStyle: {
            fontFamily: 'Cairo-SemiBold',
          },
        }}
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
