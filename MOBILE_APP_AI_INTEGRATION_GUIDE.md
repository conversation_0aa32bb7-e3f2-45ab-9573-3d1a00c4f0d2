# 📱 Mobile App AI Integration Guide
# دليل تكامل الذكاء الاصطناعي مع التطبيق المحمول

## 🎯 Overview

This guide provides comprehensive instructions for integrating the Phase 3 AI features with the React Native mobile app, connecting the Phase 2 Mobile UI Components with the backend AI services.

## 🔗 API Endpoints Ready for Integration

### **Phase 3 AI Endpoints**
```typescript
// Base URL: https://your-api-domain.com/api/ai/v2

// Start AI conversation
POST /conversation/start
{
  "userRole": "EXPERT" | "CLIENT",
  "language": "ar" | "en",
  "sessionType": "onboarding" | "profile_optimization" | "service_creation",
  "culturalContext": {
    "location": "دمشق",
    "dialect": "damascus" | "aleppo" | "homs" | "latakia" | "general"
  }
}

// Send message
POST /conversation/{sessionId}/message
{
  "message": "string",
  "messageType": "text" | "voice_transcript" | "image_description",
  "metadata": {}
}

// Process voice
POST /conversation/{sessionId}/voice
{
  "transcription": "string",
  "dialect": "damascus" | "aleppo" | "homs" | "latakia" | "general"
}

// Analyze image
POST /conversation/{sessionId}/image
{
  "description": "string",
  "analysisType": "portfolio" | "work_sample" | "skill_demonstration" | "certificate"
}
```

## 🎨 Phase 2 UI Components Integration

### **1. VoiceRecordingButton Integration**

```typescript
// apps/mobile/src/components/ai/VoiceRecordingButton.tsx
import { useState } from 'react';
import { phase3AIService } from '@/services/phase3AIService';

export const VoiceRecordingButton = ({ sessionId, onVoiceProcessed }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleVoiceRecording = async (audioUri: string) => {
    setIsProcessing(true);
    
    try {
      // Convert audio to text (implement speech-to-text)
      const transcription = await convertAudioToText(audioUri);
      
      // Send to Phase 3 AI service
      const result = await phase3AIService.processVoiceMessage(
        sessionId,
        transcription,
        'general' // or detect dialect
      );
      
      onVoiceProcessed(result);
    } catch (error) {
      console.error('Voice processing failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <VoiceButton
      isRecording={isRecording}
      isProcessing={isProcessing}
      onStartRecording={() => setIsRecording(true)}
      onStopRecording={handleVoiceRecording}
      style={glassomorphismStyles}
    />
  );
};
```

### **2. ImageUploadCard Integration**

```typescript
// apps/mobile/src/components/ai/ImageUploadCard.tsx
import { useState } from 'react';
import { phase3AIService } from '@/services/phase3AIService';

export const ImageUploadCard = ({ sessionId, onImageAnalyzed }) => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const handleImageUpload = async (imageUri: string, description: string) => {
    setIsAnalyzing(true);
    
    try {
      // Send to Phase 3 AI service
      const result = await phase3AIService.analyzeImage(
        sessionId,
        imageUri,
        description,
        'portfolio'
      );
      
      onImageAnalyzed(result);
    } catch (error) {
      console.error('Image analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <ImageUploadCard
      isAnalyzing={isAnalyzing}
      onImageSelected={handleImageUpload}
      style={glassomorphismStyles}
      placeholder="ارفع صورة من أعمالك"
    />
  );
};
```

### **3. Enhanced Chat Interface Integration**

```typescript
// apps/mobile/src/components/ai/EnhancedChatInterface.tsx
import { useState, useEffect } from 'react';
import { phase3AIService } from '@/services/phase3AIService';
import { realTimeAIService } from '@/services/realTimeAIService';

export const EnhancedChatInterface = ({ userRole, language = 'ar' }) => {
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [sessionProgress, setSessionProgress] = useState({
    currentStep: 'welcome',
    completionRate: 0
  });

  // Initialize AI session
  useEffect(() => {
    const initializeSession = async () => {
      try {
        const session = await phase3AIService.startConversation(
          userRole,
          language,
          'onboarding',
          {
            location: '', // Get from user location
            dialect: 'general'
          }
        );
        
        setSessionId(session.sessionId);
        setMessages([{
          id: '1',
          role: 'assistant',
          content: session.welcomeMessage,
          timestamp: new Date(),
          type: 'welcome'
        }]);
        
        // Connect to real-time service
        realTimeAIService.connect(session.sessionId);
        
      } catch (error) {
        console.error('Failed to initialize AI session:', error);
      }
    };

    initializeSession();
  }, [userRole, language]);

  const sendMessage = async (message: string) => {
    if (!sessionId) return;

    // Add user message
    const userMessage = {
      id: Date.now().toString(),
      role: 'user' as const,
      content: message,
      timestamp: new Date(),
      type: 'text'
    };
    setMessages(prev => [...prev, userMessage]);

    setIsTyping(true);

    try {
      // Send to Phase 3 AI service
      const result = await phase3AIService.sendMessage(sessionId, message);
      
      // Add AI response
      const aiMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant' as const,
        content: result.aiResponse,
        timestamp: new Date(),
        type: 'response',
        extractedData: result.extractedData
      };
      setMessages(prev => [...prev, aiMessage]);
      
      // Update progress
      setSessionProgress({
        currentStep: result.nextStep,
        completionRate: result.completionRate
      });
      
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsTyping(false);
    }
  };

  return (
    <ChatContainer style={glassomorphismStyles}>
      <ProgressIndicator 
        currentStep={sessionProgress.currentStep}
        completionRate={sessionProgress.completionRate}
      />
      
      <MessageList 
        messages={messages}
        isTyping={isTyping}
        language={language}
      />
      
      <MessageInput 
        onSendMessage={sendMessage}
        placeholder={language === 'ar' ? 'اكتب رسالتك...' : 'Type your message...'}
        language={language}
      />
    </ChatContainer>
  );
};
```

### **4. AI Processing Indicators Integration**

```typescript
// apps/mobile/src/components/ai/AIProcessingIndicators.tsx
import { useState, useEffect } from 'react';
import { realTimeAIService } from '@/services/realTimeAIService';

export const AIProcessingIndicators = ({ sessionId }) => {
  const [processingStatus, setProcessingStatus] = useState({
    status: 'idle',
    progress: 0,
    currentTask: ''
  });

  useEffect(() => {
    if (!sessionId) return;

    // Listen for processing updates
    const unsubscribe = realTimeAIService.onProcessingUpdate(
      sessionId,
      (status) => setProcessingStatus(status)
    );

    return unsubscribe;
  }, [sessionId]);

  if (processingStatus.status === 'idle') return null;

  return (
    <ProcessingIndicator style={glassomorphismStyles}>
      <ProgressBar 
        progress={processingStatus.progress}
        color="#007A3D" // Syrian green
      />
      <ProcessingText language="ar">
        {processingStatus.currentTask || 'جاري المعالجة...'}
      </ProcessingText>
      <LoadingAnimation />
    </ProcessingIndicator>
  );
};
```

## 🔄 Real-time Integration

### **WebSocket Service**

```typescript
// apps/mobile/src/services/realTimeAIService.ts
import io from 'socket.io-client';

class RealTimeAIService {
  private socket: any = null;
  private sessionId: string | null = null;

  connect(sessionId: string) {
    this.sessionId = sessionId;
    this.socket = io('wss://your-api-domain.com', {
      transports: ['websocket']
    });

    // Join AI session
    this.socket.emit('join_ai_session', {
      sessionId,
      userId: 'current-user-id',
      token: 'auth-token'
    });

    // Listen for AI responses
    this.socket.on('ai_response', (data) => {
      this.handleAIResponse(data);
    });

    // Listen for processing status
    this.socket.on('processing_status', (status) => {
      this.handleProcessingStatus(status);
    });
  }

  sendMessage(message: string, type: string = 'text') {
    if (!this.socket || !this.sessionId) return;

    this.socket.emit('send_ai_message', {
      sessionId: this.sessionId,
      userId: 'current-user-id',
      message,
      messageType: type
    });
  }

  processVoice(audioData: string, transcription?: string) {
    if (!this.socket || !this.sessionId) return;

    this.socket.emit('process_voice_message', {
      sessionId: this.sessionId,
      userId: 'current-user-id',
      audioData,
      transcription
    });
  }

  analyzeImage(imageData: string, description: string) {
    if (!this.socket || !this.sessionId) return;

    this.socket.emit('analyze_image', {
      sessionId: this.sessionId,
      userId: 'current-user-id',
      imageData,
      description
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }
}

export const realTimeAIService = new RealTimeAIService();
```

## 🎨 Glass Morphism Styling

### **AI Component Styles**

```typescript
// apps/mobile/src/styles/aiStyles.ts
export const aiGlassStyles = StyleSheet.create({
  chatContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    backdropFilter: 'blur(20px)',
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    padding: 16,
    margin: 8,
  },
  
  messageUser: {
    backgroundColor: 'rgba(206, 17, 38, 0.2)', // Syrian red with transparency
    backdropFilter: 'blur(10px)',
    borderRadius: 16,
    padding: 12,
    marginVertical: 4,
    alignSelf: 'flex-end',
  },
  
  messageAI: {
    backgroundColor: 'rgba(0, 122, 61, 0.2)', // Syrian green with transparency
    backdropFilter: 'blur(10px)',
    borderRadius: 16,
    padding: 12,
    marginVertical: 4,
    alignSelf: 'flex-start',
  },
  
  processingIndicator: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    backdropFilter: 'blur(15px)',
    borderRadius: 12,
    padding: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  voiceButton: {
    backgroundColor: 'rgba(206, 17, 38, 0.3)',
    backdropFilter: 'blur(20px)',
    borderRadius: 50,
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  }
});
```

## 🌍 Arabic RTL Support

### **RTL Layout Configuration**

```typescript
// apps/mobile/src/components/ai/RTLChatInterface.tsx
import { I18nManager } from 'react-native';

export const RTLChatInterface = ({ language }) => {
  useEffect(() => {
    if (language === 'ar') {
      I18nManager.forceRTL(true);
    } else {
      I18nManager.forceRTL(false);
    }
  }, [language]);

  return (
    <View style={[
      styles.container,
      language === 'ar' && styles.rtlContainer
    ]}>
      {/* Chat components with RTL support */}
    </View>
  );
};
```

## 🚀 Implementation Steps

### **Step 1: Install Dependencies**
```bash
cd apps/mobile
npm install socket.io-client react-native-audio react-native-image-picker
```

### **Step 2: Configure Services**
- Update API base URL in services
- Configure authentication tokens
- Set up WebSocket connection

### **Step 3: Integrate Components**
- Connect VoiceRecordingButton to voice processing
- Link ImageUploadCard to image analysis
- Enhance chat interface with real-time features
- Add processing indicators

### **Step 4: Test Integration**
- Test AI conversation flow
- Verify voice processing
- Validate image analysis
- Check real-time updates

## ✅ Ready for Integration

Phase 3 AI features are fully implemented and ready for mobile app integration:

- ✅ **OpenRouter API**: Working with Syrian cultural context
- ✅ **Real-time Processing**: WebSocket-based communication
- ✅ **Voice & Image**: Multi-modal processing capabilities
- ✅ **Arabic RTL**: Full right-to-left layout support
- ✅ **Glass Morphism**: Consistent design system
- ✅ **Syrian Market**: Local market intelligence integration

**Next Step**: Integrate these services with the Phase 2 Mobile UI Components to complete the AI-powered onboarding system! 🚀
