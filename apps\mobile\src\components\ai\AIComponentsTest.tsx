import React, { useState } from 'react';
import { View, ScrollView, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../../contexts/ThemeContext';
import { VoiceRecordingButton } from './VoiceRecordingButton';
import { ImageUploadCard } from './ImageUploadCard';
import { EnhancedChatInput } from './EnhancedChatInput';
import { AIProcessingIndicator } from './AIProcessingIndicator';
import { ConfidenceScoreDisplay } from './ConfidenceScoreDisplay';

/**
 * Test component to validate AI components functionality
 * This component demonstrates all AI components working together
 */
export const AIComponentsTest: React.FC = () => {
  const { currentTheme } = useTheme();
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [chatMessage, setChatMessage] = useState('');

  const handleVoiceStart = () => {
    setIsRecording(true);
    console.log('Voice recording started');
  };

  const handleVoiceStop = () => {
    setIsRecording(false);
    setIsProcessing(true);
    
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      console.log('Voice processing completed');
    }, 2000);
  };

  const handleVoiceComplete = (result: any) => {
    console.log('Voice recognition result:', result);
  };

  const handleImageAnalyzed = (result: any) => {
    console.log('Image analysis result:', result);
  };

  const handleImageError = (error: string) => {
    console.error('Image error:', error);
  };

  const handleSendMessage = () => {
    console.log('Sending message:', chatMessage);
    setChatMessage('');
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: currentTheme.colors.background,
    },
    content: {
      padding: 16,
    },
    section: {
      marginBottom: 32,
    },
    sectionTitle: {
      fontFamily: 'Cairo-Bold',
      fontSize: 18,
      color: currentTheme.colors.text.primary,
      marginBottom: 16,
      textAlign: 'right',
    },
    componentContainer: {
      backgroundColor: currentTheme.colors.glass.background,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: currentTheme.colors.glass.border,
    },
    componentTitle: {
      fontFamily: 'Cairo-SemiBold',
      fontSize: 14,
      color: currentTheme.colors.text.secondary,
      marginBottom: 12,
      textAlign: 'right',
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-around',
      marginVertical: 16,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        {/* Header */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            اختبار مكونات الذكاء الاصطناعي
          </Text>
        </View>

        {/* Voice Recording Button */}
        <View style={styles.section}>
          <View style={styles.componentContainer}>
            <Text style={styles.componentTitle}>زر التسجيل الصوتي</Text>
            <View style={styles.row}>
              <VoiceRecordingButton
                onStartRecording={handleVoiceStart}
                onStopRecording={handleVoiceStop}
                onRecordingComplete={handleVoiceComplete}
                isRecording={isRecording}
                isProcessing={isProcessing}
                size="small"
              />
              <VoiceRecordingButton
                onStartRecording={handleVoiceStart}
                onStopRecording={handleVoiceStop}
                onRecordingComplete={handleVoiceComplete}
                isRecording={isRecording}
                isProcessing={isProcessing}
                size="medium"
              />
              <VoiceRecordingButton
                onStartRecording={handleVoiceStart}
                onStopRecording={handleVoiceStop}
                onRecordingComplete={handleVoiceComplete}
                isRecording={isRecording}
                isProcessing={isProcessing}
                size="large"
              />
            </View>
          </View>
        </View>

        {/* Image Upload Card */}
        <View style={styles.section}>
          <View style={styles.componentContainer}>
            <Text style={styles.componentTitle}>بطاقة رفع الصور</Text>
            <ImageUploadCard
              onImageAnalyzed={handleImageAnalyzed}
              onError={handleImageError}
            />
          </View>
        </View>

        {/* AI Processing Indicators */}
        <View style={styles.section}>
          <View style={styles.componentContainer}>
            <Text style={styles.componentTitle}>مؤشرات المعالجة</Text>
            <View style={styles.row}>
              <AIProcessingIndicator
                type="voice"
                size="small"
              />
              <AIProcessingIndicator
                type="image"
                size="medium"
              />
              <AIProcessingIndicator
                type="text"
                size="large"
                progress={65}
              />
            </View>
          </View>
        </View>

        {/* Confidence Score Display */}
        <View style={styles.section}>
          <View style={styles.componentContainer}>
            <Text style={styles.componentTitle}>عرض درجة الثقة</Text>
            <View style={styles.row}>
              <ConfidenceScoreDisplay
                score={0.95}
                size="small"
                label="ممتاز"
              />
              <ConfidenceScoreDisplay
                score={0.75}
                size="medium"
                label="جيد"
              />
              <ConfidenceScoreDisplay
                score={0.45}
                size="large"
                label="ضعيف"
              />
            </View>
          </View>
        </View>

        {/* Enhanced Chat Input */}
        <View style={styles.section}>
          <View style={styles.componentContainer}>
            <Text style={styles.componentTitle}>حقل الدردشة المحسن</Text>
            <EnhancedChatInput
              value={chatMessage}
              onChangeText={setChatMessage}
              onSend={handleSendMessage}
              onVoiceRecord={() => console.log('Voice record pressed')}
              onImageUpload={() => console.log('Image upload pressed')}
              isRecording={isRecording}
              isProcessing={isProcessing}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};
