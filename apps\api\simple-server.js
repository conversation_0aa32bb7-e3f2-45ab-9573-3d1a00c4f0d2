require('dotenv').config();
const express = require('express');
const cors = require('cors');

console.log('Starting simple API server...');
console.log('Environment variables loaded:');
console.log('- SUPABASE_URL:', process.env.SUPABASE_URL ? 'configured' : 'not configured');
console.log('- SUPABASE_ANON_KEY:', process.env.SUPABASE_ANON_KEY ? 'configured' : 'not configured');
console.log('- OPENROUTER_API_KEY:', process.env.OPENROUTER_API_KEY ? 'configured' : 'not configured');

const app = express();
const PORT = 3001;

// Basic middleware
app.use(cors());
app.use(express.json());

// Health endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    message: 'Simple API server is running'
  });
});

// Test connection endpoint
app.get('/api/v1/test-connection', (req, res) => {
  res.json({
    success: true,
    message: 'API connection test successful',
    timestamp: new Date().toISOString(),
    supabase: {
      url: process.env.SUPABASE_URL ? 'configured' : 'not configured',
      anonKey: process.env.SUPABASE_ANON_KEY ? 'configured' : 'not configured'
    },
    openrouter: {
      apiKey: process.env.OPENROUTER_API_KEY ? 'configured' : 'not configured'
    }
  });
});

// AI test endpoint
app.get('/api/v1/ai/test-connection', (req, res) => {
  res.json({
    success: true,
    message: 'AI endpoint is accessible',
    timestamp: new Date().toISOString(),
    openrouter: {
      configured: !!process.env.OPENROUTER_API_KEY,
      status: 'ready'
    },
    supabase: {
      configured: !!(process.env.SUPABASE_URL && process.env.SUPABASE_ANON_KEY),
      status: 'ready'
    }
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Freela Syria API Server (Simple)',
    version: 'v1',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      testConnection: '/api/v1/test-connection',
      aiTest: '/api/v1/ai/test-connection'
    }
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Simple API server running on port ${PORT}`);
  console.log(`🏥 Health: http://localhost:${PORT}/health`);
  console.log(`🔗 Test: http://localhost:${PORT}/api/v1/test-connection`);
  console.log(`🤖 AI Test: http://localhost:${PORT}/api/v1/ai/test-connection`);
});
